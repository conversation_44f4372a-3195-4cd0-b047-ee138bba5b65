/****************************************************************************
** Meta object code from reading C++ file 'EmbeddedHVNCServer.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/EmbeddedHVNCServer.h"
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'EmbeddedHVNCServer.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS = QtMocHelpers::stringData(
    "HVNCServerThread",
    "serverStarted",
    "",
    "port",
    "serverStopped",
    "clientConnected",
    "clientInfo",
    "clientDisconnected",
    "serverError",
    "error"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS_t {
    uint offsetsAndSizes[20];
    char stringdata0[17];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[5];
    char stringdata4[14];
    char stringdata5[16];
    char stringdata6[11];
    char stringdata7[19];
    char stringdata8[12];
    char stringdata9[6];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS_t qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS = {
    {
        QT_MOC_LITERAL(0, 16),  // "HVNCServerThread"
        QT_MOC_LITERAL(17, 13),  // "serverStarted"
        QT_MOC_LITERAL(31, 0),  // ""
        QT_MOC_LITERAL(32, 4),  // "port"
        QT_MOC_LITERAL(37, 13),  // "serverStopped"
        QT_MOC_LITERAL(51, 15),  // "clientConnected"
        QT_MOC_LITERAL(67, 10),  // "clientInfo"
        QT_MOC_LITERAL(78, 18),  // "clientDisconnected"
        QT_MOC_LITERAL(97, 11),  // "serverError"
        QT_MOC_LITERAL(109, 5)   // "error"
    },
    "HVNCServerThread",
    "serverStarted",
    "",
    "port",
    "serverStopped",
    "clientConnected",
    "clientInfo",
    "clientDisconnected",
    "serverError",
    "error"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSHVNCServerThreadENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
       5,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,   44,    2, 0x06,    1 /* Public */,
       4,    0,   47,    2, 0x06,    3 /* Public */,
       5,    1,   48,    2, 0x06,    4 /* Public */,
       7,    1,   51,    2, 0x06,    6 /* Public */,
       8,    1,   54,    2, 0x06,    8 /* Public */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int,    3,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QString,    9,

       0        // eod
};

Q_CONSTINIT const QMetaObject HVNCServerThread::staticMetaObject = { {
    QMetaObject::SuperData::link<QThread::staticMetaObject>(),
    qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSHVNCServerThreadENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<HVNCServerThread, std::true_type>,
        // method 'serverStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'serverStopped'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'clientConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clientDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'serverError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>
    >,
    nullptr
} };

void HVNCServerThread::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<HVNCServerThread *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->serverStarted((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 1: _t->serverStopped(); break;
        case 2: _t->clientConnected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->clientDisconnected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->serverError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (HVNCServerThread::*)(int );
            if (_t _q_method = &HVNCServerThread::serverStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (HVNCServerThread::*)();
            if (_t _q_method = &HVNCServerThread::serverStopped; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (HVNCServerThread::*)(const QString & );
            if (_t _q_method = &HVNCServerThread::clientConnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (HVNCServerThread::*)(const QString & );
            if (_t _q_method = &HVNCServerThread::clientDisconnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (HVNCServerThread::*)(const QString & );
            if (_t _q_method = &HVNCServerThread::serverError; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
    }
}

const QMetaObject *HVNCServerThread::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *HVNCServerThread::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSHVNCServerThreadENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QThread::qt_metacast(_clname);
}

int HVNCServerThread::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QThread::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 5)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 5;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 5)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 5;
    }
    return _id;
}

// SIGNAL 0
void HVNCServerThread::serverStarted(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void HVNCServerThread::serverStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void HVNCServerThread::clientConnected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void HVNCServerThread::clientDisconnected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void HVNCServerThread::serverError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS = QtMocHelpers::stringData(
    "EmbeddedHVNCServer",
    "serverStarted",
    "",
    "host",
    "port",
    "serverStopped",
    "clientConnected",
    "clientInfo",
    "clientDisconnected",
    "serverError",
    "error",
    "applicationLaunched",
    "appName",
    "pid",
    "desktopImageReady",
    "image",
    "onServerThreadStarted",
    "onServerThreadStopped",
    "onServerThreadError"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS_t {
    uint offsetsAndSizes[38];
    char stringdata0[19];
    char stringdata1[14];
    char stringdata2[1];
    char stringdata3[5];
    char stringdata4[5];
    char stringdata5[14];
    char stringdata6[16];
    char stringdata7[11];
    char stringdata8[19];
    char stringdata9[12];
    char stringdata10[6];
    char stringdata11[20];
    char stringdata12[8];
    char stringdata13[4];
    char stringdata14[18];
    char stringdata15[6];
    char stringdata16[22];
    char stringdata17[22];
    char stringdata18[20];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS_t qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS = {
    {
        QT_MOC_LITERAL(0, 18),  // "EmbeddedHVNCServer"
        QT_MOC_LITERAL(19, 13),  // "serverStarted"
        QT_MOC_LITERAL(33, 0),  // ""
        QT_MOC_LITERAL(34, 4),  // "host"
        QT_MOC_LITERAL(39, 4),  // "port"
        QT_MOC_LITERAL(44, 13),  // "serverStopped"
        QT_MOC_LITERAL(58, 15),  // "clientConnected"
        QT_MOC_LITERAL(74, 10),  // "clientInfo"
        QT_MOC_LITERAL(85, 18),  // "clientDisconnected"
        QT_MOC_LITERAL(104, 11),  // "serverError"
        QT_MOC_LITERAL(116, 5),  // "error"
        QT_MOC_LITERAL(122, 19),  // "applicationLaunched"
        QT_MOC_LITERAL(142, 7),  // "appName"
        QT_MOC_LITERAL(150, 3),  // "pid"
        QT_MOC_LITERAL(154, 17),  // "desktopImageReady"
        QT_MOC_LITERAL(172, 5),  // "image"
        QT_MOC_LITERAL(178, 21),  // "onServerThreadStarted"
        QT_MOC_LITERAL(200, 21),  // "onServerThreadStopped"
        QT_MOC_LITERAL(222, 19)   // "onServerThreadError"
    },
    "EmbeddedHVNCServer",
    "serverStarted",
    "",
    "host",
    "port",
    "serverStopped",
    "clientConnected",
    "clientInfo",
    "clientDisconnected",
    "serverError",
    "error",
    "applicationLaunched",
    "appName",
    "pid",
    "desktopImageReady",
    "image",
    "onServerThreadStarted",
    "onServerThreadStopped",
    "onServerThreadError"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSEmbeddedHVNCServerENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      10,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    2,   74,    2, 0x06,    1 /* Public */,
       5,    0,   79,    2, 0x06,    4 /* Public */,
       6,    1,   80,    2, 0x06,    5 /* Public */,
       8,    1,   83,    2, 0x06,    7 /* Public */,
       9,    1,   86,    2, 0x06,    9 /* Public */,
      11,    2,   89,    2, 0x06,   11 /* Public */,
      14,    1,   94,    2, 0x06,   14 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      16,    1,   97,    2, 0x08,   16 /* Private */,
      17,    0,  100,    2, 0x08,   18 /* Private */,
      18,    1,  101,    2, 0x08,   19 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    4,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,    7,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QString, QMetaType::UInt,   12,   13,
    QMetaType::Void, QMetaType::QPixmap,   15,

 // slots: parameters
    QMetaType::Void, QMetaType::Int,    4,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   10,

       0        // eod
};

Q_CONSTINIT const QMetaObject EmbeddedHVNCServer::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSEmbeddedHVNCServerENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<EmbeddedHVNCServer, std::true_type>,
        // method 'serverStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'serverStopped'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'clientConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clientDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'serverError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'applicationLaunched'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<quint32, std::false_type>,
        // method 'desktopImageReady'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPixmap &, std::false_type>,
        // method 'onServerThreadStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onServerThreadStopped'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onServerThreadError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>
    >,
    nullptr
} };

void EmbeddedHVNCServer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<EmbeddedHVNCServer *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->serverStarted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 1: _t->serverStopped(); break;
        case 2: _t->clientConnected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 3: _t->clientDisconnected((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->serverError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->applicationLaunched((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<quint32>>(_a[2]))); break;
        case 6: _t->desktopImageReady((*reinterpret_cast< std::add_pointer_t<QPixmap>>(_a[1]))); break;
        case 7: _t->onServerThreadStarted((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 8: _t->onServerThreadStopped(); break;
        case 9: _t->onServerThreadError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (EmbeddedHVNCServer::*)(const QString & , int );
            if (_t _q_method = &EmbeddedHVNCServer::serverStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (EmbeddedHVNCServer::*)();
            if (_t _q_method = &EmbeddedHVNCServer::serverStopped; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (EmbeddedHVNCServer::*)(const QString & );
            if (_t _q_method = &EmbeddedHVNCServer::clientConnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (EmbeddedHVNCServer::*)(const QString & );
            if (_t _q_method = &EmbeddedHVNCServer::clientDisconnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (EmbeddedHVNCServer::*)(const QString & );
            if (_t _q_method = &EmbeddedHVNCServer::serverError; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (EmbeddedHVNCServer::*)(const QString & , quint32 );
            if (_t _q_method = &EmbeddedHVNCServer::applicationLaunched; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (EmbeddedHVNCServer::*)(const QPixmap & );
            if (_t _q_method = &EmbeddedHVNCServer::desktopImageReady; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
    }
}

const QMetaObject *EmbeddedHVNCServer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *EmbeddedHVNCServer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSEmbeddedHVNCServerENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int EmbeddedHVNCServer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 10)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 10;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 10)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 10;
    }
    return _id;
}

// SIGNAL 0
void EmbeddedHVNCServer::serverStarted(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void EmbeddedHVNCServer::serverStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void EmbeddedHVNCServer::clientConnected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void EmbeddedHVNCServer::clientDisconnected(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void EmbeddedHVNCServer::serverError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void EmbeddedHVNCServer::applicationLaunched(const QString & _t1, quint32 _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void EmbeddedHVNCServer::desktopImageReady(const QPixmap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
