{"BUILD_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen", "CMAKE_BINARY_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build", "CMAKE_CURRENT_BINARY_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build", "CMAKE_CURRENT_SOURCE_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design", "CMAKE_SOURCE_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design", "CROSS_CONFIG": false, "GENERATOR": "Visual Studio 17 2022", "INCLUDE_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include", "INCLUDE_DIR_Debug": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_Release", "INPUTS": ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/firefox.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/hvnc_icon.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/disconnect.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/settings.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/edge.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/powershell.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/connect.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/brave.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/explorer.png", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/chrome.png"], "LOCK_FILE": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Lock.lock", "MULTI_CONFIG": true, "OPTIONS": ["--no-zstd", "-name", "resources"], "OUTPUT_CHECKSUM": "3YJK5W5UP7", "OUTPUT_NAME": "qrc_resources.cpp", "RCC_EXECUTABLE": "C:/Qt/6.5.3/msvc2019_64/./bin/rcc.exe", "RCC_LIST_OPTIONS": [], "SETTINGS_FILE": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used.txt", "SETTINGS_FILE_Debug": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Used_Release.txt", "SOURCE": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/resources.qrc", "USE_BETTER_GRAPH": false, "VERBOSITY": 0}