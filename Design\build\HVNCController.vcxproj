﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{A0A54B79-3AC1-359C-B07F-8A52975938B5}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>HVNCController</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">HVNCController.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">HVNC Controller</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">HVNCController.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">HVNC Controller</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">HVNCController.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">HVNC Controller</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">HVNCController.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">HVNC Controller</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtCore" /external:I "C:/Qt/6.5.3/msvc2019_64/include" /external:I "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtGui" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target HVNCController</Message>
      <Command>setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/autouic_Debug.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.5.3\msvc2019_64\lib\Qt6Widgetsd.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Networkd.lib;kernel32.lib;user32.lib;gdi32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;Gdiplus.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Guid.lib;d3d11.lib;dxgi.lib;dxguid.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Cored.lib;mpr.lib;userenv.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPointd.lib;ws2_32.lib;shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/Debug/HVNC Controller.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/Debug/HVNC Controller.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtCore" /external:I "C:/Qt/6.5.3/msvc2019_64/include" /external:I "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtGui" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target HVNCController</Message>
      <Command>setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/autouic_Release.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.5.3\msvc2019_64\lib\Qt6Widgets.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.lib;kernel32.lib;user32.lib;gdi32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;Gdiplus.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.lib;mpr.lib;userenv.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPoint.lib;ws2_32.lib;shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/Release/HVNC Controller.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/Release/HVNC Controller.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtCore" /external:I "C:/Qt/6.5.3/msvc2019_64/include" /external:I "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtGui" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target HVNCController</Message>
      <Command>setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/autouic_MinSizeRel.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.5.3\msvc2019_64\lib\Qt6Widgets.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.lib;kernel32.lib;user32.lib;gdi32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;Gdiplus.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.lib;mpr.lib;userenv.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPoint.lib;ws2_32.lib;shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/MinSizeRel/HVNC Controller.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/MinSizeRel/HVNC Controller.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtCore" /external:I "C:/Qt/6.5.3/msvc2019_64/include" /external:I "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtGui" /external:I "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp20</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;QT_NETWORK_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;C:\Qt\6.5.3\msvc2019_64\include\QtCore;C:\Qt\6.5.3\msvc2019_64\include;C:\Qt\6.5.3\msvc2019_64\mkspecs\win32-msvc;C:\Qt\6.5.3\msvc2019_64\include\QtWidgets;C:\Qt\6.5.3\msvc2019_64\include\QtGui;C:\Qt\6.5.3\msvc2019_64\include\QtNetwork;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target HVNCController</Message>
      <Command>setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/autouic_RelWithDebInfo.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <Link>
      <AdditionalDependencies>C:\Qt\6.5.3\msvc2019_64\lib\Qt6Widgets.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Network.lib;kernel32.lib;user32.lib;gdi32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;Gdiplus.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Gui.lib;d3d11.lib;dxgi.lib;dxguid.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6Core.lib;mpr.lib;userenv.lib;C:\Qt\6.5.3\msvc2019_64\lib\Qt6EntryPoint.lib;ws2_32.lib;shell32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/RelWithDebInfo/HVNC Controller.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/RelWithDebInfo/HVNC Controller.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\467336b0bcf9497c3aff6588d4194822\autouic_(CONFIG).stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\MainWindow.ui;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\SettingsDialog.ui;C:\Qt\6.5.3\msvc2019_64\.\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_Debug.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\MainWindow.ui;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\SettingsDialog.ui;C:\Qt\6.5.3\msvc2019_64\.\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_Release.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\MainWindow.ui;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\SettingsDialog.ui;C:\Qt\6.5.3\msvc2019_64\.\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_MinSizeRel.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\MainWindow.ui;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\SettingsDialog.ui;C:\Qt\6.5.3\msvc2019_64\.\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_RelWithDebInfo.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3c20757acc493fd7a23e1318d4226ef3\qrc_resources.cpp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\HVNCController_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\firefox.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\hvnc_icon.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\disconnect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\settings.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\edge.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\powershell.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\connect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\brave.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\explorer.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\chrome.png;C:\Qt\6.5.3\msvc2019_64\.\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\HVNCController_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\firefox.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\hvnc_icon.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\disconnect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\settings.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\edge.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\powershell.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\connect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\brave.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\explorer.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\chrome.png;C:\Qt\6.5.3\msvc2019_64\.\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\HVNCController_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\firefox.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\hvnc_icon.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\disconnect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\settings.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\edge.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\powershell.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\connect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\brave.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\explorer.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\chrome.png;C:\Qt\6.5.3\msvc2019_64\.\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Automatic RCC for resources/resources.qrc</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autorcc C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutoRcc_resources_3YJK5W5UP7_Info.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\HVNCController_autogen.dir\AutoRcc_resources_3YJK5W5UP7_Info.json;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\firefox.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\hvnc_icon.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\disconnect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\settings.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\edge.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\powershell.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\connect.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\brave.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\explorer.png;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\icons\chrome.png;C:\Qt\6.5.3\msvc2019_64\.\bin\rcc.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\3YJK5W5UP7\qrc_resources.cpp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\MainWindow.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SplashScreen.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SettingsDialog.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\RemoteDesktopWidget.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ApplicationLauncher.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\HVNCClient.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\PerformanceMonitor.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ConnectionManager.cpp" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\MainWindow.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SplashScreen.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SettingsDialog.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\RemoteDesktopWidget.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ApplicationLauncher.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\HVNCClient.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\PerformanceMonitor.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ConnectionManager.h" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\MainWindow.ui">
    </None>
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\SettingsDialog.ui">
    </None>
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc">
    </None>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug\ui\ui_MainWindow.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug\ui\ui_SettingsDialog.h" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_Debug.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\3YJK5W5UP7\qrc_resources.cpp" />
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release\ui\ui_MainWindow.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release\ui\ui_SettingsDialog.h" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_Release.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel\ui\ui_MainWindow.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel\ui\ui_SettingsDialog.h" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_MinSizeRel.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo\ui\ui_MainWindow.h" />
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo\ui\ui_SettingsDialog.h" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_RelWithDebInfo.stamp">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\ZERO_CHECK.vcxproj">
      <Project>{FB37A46C-E3C3-365F-9A0B-228A61234086}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>