#pragma once

#include <QWidget>
#include <QTimer>
#include <QProgressBar>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPainter>
#include <QPixmap>
#include <QFont>
#include <QPropertyAnimation>
#include <QGraphicsOpacityEffect>

class SplashScreen : public QWidget
{
    Q_OBJECT

public:
    explicit SplashScreen(QWidget *parent = nullptr);
    ~SplashScreen();

    void startAnimation();

signals:
    void finished();

protected:
    void paintEvent(QPaintEvent *event) override;
    void showEvent(QShowEvent *event) override;

private slots:
    void updateProgress();
    void onAnimationFinished();

private:
    void setupUI();
    void createLogo();
    
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_logoLayout;
    QLabel *m_logoLabel;
    QLabel *m_titleLabel;
    QLabel *m_versionLabel;
    QLabel *m_statusLabel;
    QProgressBar *m_progressBar;
    
    QTimer *m_progressTimer;
    QPropertyAnimation *m_fadeAnimation;
    QGraphicsOpacityEffect *m_opacityEffect;
    
    int m_currentProgress;
    QStringList m_loadingMessages;
    int m_messageIndex;
    
    static constexpr int SPLASH_WIDTH = 500;
    static constexpr int SPLASH_HEIGHT = 300;
    static constexpr int ANIMATION_DURATION = 3000; // 3 seconds
};
