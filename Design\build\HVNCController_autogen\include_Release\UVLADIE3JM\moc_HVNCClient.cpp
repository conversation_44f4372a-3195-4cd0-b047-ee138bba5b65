/****************************************************************************
** Meta object code from reading C++ file 'HVNCClient.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/HVNCClient.h"
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'HVNCClient.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSHVNCClientENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSHVNCClientENDCLASS = QtMocHelpers::stringData(
    "HVNCClient",
    "connected",
    "",
    "disconnected",
    "desktopImageReceived",
    "image",
    "applicationLaunched",
    "appName",
    "pid",
    "errorOccurred",
    "error",
    "serverStarted",
    "host",
    "port",
    "serverStopped",
    "serverError",
    "onConnected",
    "onDisconnected",
    "onReadyRead",
    "onError",
    "QAbstractSocket::SocketError",
    "requestDesktopUpdate",
    "onServerProcessFinished",
    "exitCode",
    "QProcess::ExitStatus",
    "exitStatus",
    "onServerProcessError",
    "QProcess::ProcessError"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSHVNCClientENDCLASS_t {
    uint offsetsAndSizes[56];
    char stringdata0[11];
    char stringdata1[10];
    char stringdata2[1];
    char stringdata3[13];
    char stringdata4[21];
    char stringdata5[6];
    char stringdata6[20];
    char stringdata7[8];
    char stringdata8[4];
    char stringdata9[14];
    char stringdata10[6];
    char stringdata11[14];
    char stringdata12[5];
    char stringdata13[5];
    char stringdata14[14];
    char stringdata15[12];
    char stringdata16[12];
    char stringdata17[15];
    char stringdata18[12];
    char stringdata19[8];
    char stringdata20[29];
    char stringdata21[21];
    char stringdata22[24];
    char stringdata23[9];
    char stringdata24[21];
    char stringdata25[11];
    char stringdata26[21];
    char stringdata27[23];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSHVNCClientENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSHVNCClientENDCLASS_t qt_meta_stringdata_CLASSHVNCClientENDCLASS = {
    {
        QT_MOC_LITERAL(0, 10),  // "HVNCClient"
        QT_MOC_LITERAL(11, 9),  // "connected"
        QT_MOC_LITERAL(21, 0),  // ""
        QT_MOC_LITERAL(22, 12),  // "disconnected"
        QT_MOC_LITERAL(35, 20),  // "desktopImageReceived"
        QT_MOC_LITERAL(56, 5),  // "image"
        QT_MOC_LITERAL(62, 19),  // "applicationLaunched"
        QT_MOC_LITERAL(82, 7),  // "appName"
        QT_MOC_LITERAL(90, 3),  // "pid"
        QT_MOC_LITERAL(94, 13),  // "errorOccurred"
        QT_MOC_LITERAL(108, 5),  // "error"
        QT_MOC_LITERAL(114, 13),  // "serverStarted"
        QT_MOC_LITERAL(128, 4),  // "host"
        QT_MOC_LITERAL(133, 4),  // "port"
        QT_MOC_LITERAL(138, 13),  // "serverStopped"
        QT_MOC_LITERAL(152, 11),  // "serverError"
        QT_MOC_LITERAL(164, 11),  // "onConnected"
        QT_MOC_LITERAL(176, 14),  // "onDisconnected"
        QT_MOC_LITERAL(191, 11),  // "onReadyRead"
        QT_MOC_LITERAL(203, 7),  // "onError"
        QT_MOC_LITERAL(211, 28),  // "QAbstractSocket::SocketError"
        QT_MOC_LITERAL(240, 20),  // "requestDesktopUpdate"
        QT_MOC_LITERAL(261, 23),  // "onServerProcessFinished"
        QT_MOC_LITERAL(285, 8),  // "exitCode"
        QT_MOC_LITERAL(294, 20),  // "QProcess::ExitStatus"
        QT_MOC_LITERAL(315, 10),  // "exitStatus"
        QT_MOC_LITERAL(326, 20),  // "onServerProcessError"
        QT_MOC_LITERAL(347, 22)   // "QProcess::ProcessError"
    },
    "HVNCClient",
    "connected",
    "",
    "disconnected",
    "desktopImageReceived",
    "image",
    "applicationLaunched",
    "appName",
    "pid",
    "errorOccurred",
    "error",
    "serverStarted",
    "host",
    "port",
    "serverStopped",
    "serverError",
    "onConnected",
    "onDisconnected",
    "onReadyRead",
    "onError",
    "QAbstractSocket::SocketError",
    "requestDesktopUpdate",
    "onServerProcessFinished",
    "exitCode",
    "QProcess::ExitStatus",
    "exitStatus",
    "onServerProcessError",
    "QProcess::ProcessError"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSHVNCClientENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  104,    2, 0x06,    1 /* Public */,
       3,    0,  105,    2, 0x06,    2 /* Public */,
       4,    1,  106,    2, 0x06,    3 /* Public */,
       6,    2,  109,    2, 0x06,    5 /* Public */,
       9,    1,  114,    2, 0x06,    8 /* Public */,
      11,    2,  117,    2, 0x06,   10 /* Public */,
      14,    0,  122,    2, 0x06,   13 /* Public */,
      15,    1,  123,    2, 0x06,   14 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      16,    0,  126,    2, 0x08,   16 /* Private */,
      17,    0,  127,    2, 0x08,   17 /* Private */,
      18,    0,  128,    2, 0x08,   18 /* Private */,
      19,    1,  129,    2, 0x08,   19 /* Private */,
      21,    0,  132,    2, 0x08,   21 /* Private */,
      22,    2,  133,    2, 0x08,   22 /* Private */,
      26,    1,  138,    2, 0x08,   25 /* Private */,

 // signals: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPixmap,    5,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    7,    8,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   12,   13,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   10,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 20,   10,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, 0x80000000 | 24,   23,   25,
    QMetaType::Void, 0x80000000 | 27,   10,

       0        // eod
};

Q_CONSTINIT const QMetaObject HVNCClient::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSHVNCClientENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSHVNCClientENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSHVNCClientENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<HVNCClient, std::true_type>,
        // method 'connected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'disconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'desktopImageReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPixmap &, std::false_type>,
        // method 'applicationLaunched'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'errorOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'serverStarted'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'serverStopped'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'serverError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onReadyRead'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractSocket::SocketError, std::false_type>,
        // method 'requestDesktopUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onServerProcessFinished'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<QProcess::ExitStatus, std::false_type>,
        // method 'onServerProcessError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QProcess::ProcessError, std::false_type>
    >,
    nullptr
} };

void HVNCClient::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<HVNCClient *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->connected(); break;
        case 1: _t->disconnected(); break;
        case 2: _t->desktopImageReceived((*reinterpret_cast< std::add_pointer_t<QPixmap>>(_a[1]))); break;
        case 3: _t->applicationLaunched((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 4: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->serverStarted((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 6: _t->serverStopped(); break;
        case 7: _t->serverError((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->onConnected(); break;
        case 9: _t->onDisconnected(); break;
        case 10: _t->onReadyRead(); break;
        case 11: _t->onError((*reinterpret_cast< std::add_pointer_t<QAbstractSocket::SocketError>>(_a[1]))); break;
        case 12: _t->requestDesktopUpdate(); break;
        case 13: _t->onServerProcessFinished((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QProcess::ExitStatus>>(_a[2]))); break;
        case 14: _t->onServerProcessError((*reinterpret_cast< std::add_pointer_t<QProcess::ProcessError>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 11:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (HVNCClient::*)();
            if (_t _q_method = &HVNCClient::connected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)();
            if (_t _q_method = &HVNCClient::disconnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)(const QPixmap & );
            if (_t _q_method = &HVNCClient::desktopImageReceived; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)(const QString & , int );
            if (_t _q_method = &HVNCClient::applicationLaunched; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)(const QString & );
            if (_t _q_method = &HVNCClient::errorOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)(const QString & , int );
            if (_t _q_method = &HVNCClient::serverStarted; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)();
            if (_t _q_method = &HVNCClient::serverStopped; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (HVNCClient::*)(const QString & );
            if (_t _q_method = &HVNCClient::serverError; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
    }
}

const QMetaObject *HVNCClient::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *HVNCClient::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSHVNCClientENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int HVNCClient::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void HVNCClient::connected()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void HVNCClient::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void HVNCClient::desktopImageReceived(const QPixmap & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void HVNCClient::applicationLaunched(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void HVNCClient::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void HVNCClient::serverStarted(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void HVNCClient::serverStopped()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void HVNCClient::serverError(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
