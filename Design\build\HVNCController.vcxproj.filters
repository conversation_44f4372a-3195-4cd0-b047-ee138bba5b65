﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\MainWindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SplashScreen.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SettingsDialog.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\RemoteDesktopWidget.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ApplicationLauncher.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\HVNCClient.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\PerformanceMonitor.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ConnectionManager.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\common\SimpleLogger.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\3YJK5W5UP7\qrc_resources.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\MainWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SplashScreen.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\SettingsDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\RemoteDesktopWidget.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ApplicationLauncher.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\HVNCClient.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\PerformanceMonitor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\src\ConnectionManager.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug\ui\ui_MainWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Debug\ui\ui_SettingsDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release\ui\ui_MainWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_Release\ui\ui_SettingsDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel\ui\ui_MainWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_MinSizeRel\ui\ui_SettingsDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo\ui\ui_MainWindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\include_RelWithDebInfo\ui\ui_SettingsDialog.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\467336b0bcf9497c3aff6588d4194822\autouic_(CONFIG).stamp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3c20757acc493fd7a23e1318d4226ef3\qrc_resources.cpp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\MainWindow.ui" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\ui\SettingsDialog.ui" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_Debug.stamp" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_Release.stamp" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_MinSizeRel.stamp" />
    <None Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController_autogen\autouic_RelWithDebInfo.stamp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{4EBD61D8-E53A-3970-A989-3E6A841981EC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{137C5E61-6EDD-3ECE-BD85-DD214FAB45DC}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{DAD01C29-CE0E-315E-97F9-F3C9580EAC1F}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
