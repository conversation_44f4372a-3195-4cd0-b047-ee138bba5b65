#pragma once

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QPixmap>
#include <QByteArray>
#include <QDataStream>

class HVNCClient : public QObject
{
    Q_OBJECT

public:
    explicit HVNCClient(QObject *parent = nullptr);
    ~HVNCClient();

    bool connectToServer(const QString &host, int port);
    void disconnectFromServer();
    bool isConnected() const;
    
    void sendMouseEvent(QMouseEvent *event);
    void sendKeyEvent(QKeyEvent *event);
    void sendWheelEvent(QWheelEvent *event);
    
    void launchApplication(const QString &appName);
    void setImageQuality(int quality);
    void setResolutionScale(float scale);

signals:
    void connected();
    void disconnected();
    void desktopImageReceived(const QPixmap &image);
    void applicationLaunched(const QString &appName, int pid);
    void errorOccurred(const QString &error);

private slots:
    void onConnected();
    void onDisconnected();
    void onReadyRead();
    void onError(QAbstractSocket::SocketError error);
    void requestDesktopUpdate();

private:
    void processIncomingData();
    void sendCommand(const QString &command, const QByteArray &data = QByteArray());
    
    QTcpSocket *m_socket;
    QTimer *m_updateTimer;
    QByteArray m_buffer;
    
    bool m_isConnected;
    int m_imageQuality;
    float m_resolutionScale;
    
    static constexpr int UPDATE_INTERVAL = 16; // ~60 FPS
    static constexpr int HEADER_SIZE = 8; // Command header size
};
