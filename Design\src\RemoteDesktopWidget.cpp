#include "RemoteDesktopWidget.h"
#include <QPainter>
#include <QFont>
#include <QFontMetrics>
#include <QApplication>

RemoteDesktopWidget::RemoteDesktopWidget(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_scrollArea(nullptr)
    , m_displayWidget(nullptr)
    , m_updateTimer(new QTimer(this))
    , m_scaleFactor(1.0f)
    , m_imageQuality(85)
    , m_colorDepth(32)
    , m_isConnected(false)
    , m_currentFPS(0)
    , m_currentLatency(0)
    , m_mousePressed(false)
{
    setupUI();
    
    // Set focus policy to receive keyboard events
    setFocusPolicy(Qt::StrongFocus);
    
    // Setup update timer
    connect(m_updateTimer, &QTimer::timeout, this, &RemoteDesktopWidget::updateDisplay);
    m_updateTimer->start(UPDATE_INTERVAL);
}

RemoteDesktopWidget::~RemoteDesktopWidget() = default;

void RemoteDesktopWidget::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    // Create scroll area for desktop display
    m_scrollArea = new QScrollArea(this);
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_scrollArea->setStyleSheet(R"(
        QScrollArea {
            border: none;
            background-color: #1a1a1a;
        }
        QScrollBar:vertical {
            background-color: #404040;
            width: 12px;
            border-radius: 6px;
        }
        QScrollBar::handle:vertical {
            background-color: #666666;
            border-radius: 6px;
            min-height: 20px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #777777;
        }
        QScrollBar:horizontal {
            background-color: #404040;
            height: 12px;
            border-radius: 6px;
        }
        QScrollBar::handle:horizontal {
            background-color: #666666;
            border-radius: 6px;
            min-width: 20px;
        }
        QScrollBar::handle:horizontal:hover {
            background-color: #777777;
        }
    )");
    
    m_displayWidget = new QWidget();
    m_displayWidget->setMinimumSize(800, 600);
    m_displayWidget->setStyleSheet("background-color: #1a1a1a;");
    
    m_scrollArea->setWidget(m_displayWidget);
    m_mainLayout->addWidget(m_scrollArea);
}

void RemoteDesktopWidget::setDesktopImage(const QPixmap &pixmap)
{
    m_desktopImage = pixmap;
    
    if (!pixmap.isNull()) {
        m_isConnected = true;
        
        // Scale the image if needed
        if (m_scaleFactor != 1.0f) {
            QSize scaledSize = pixmap.size() * m_scaleFactor;
            m_scaledImage = pixmap.scaled(scaledSize, Qt::KeepAspectRatio, Qt::SmoothTransformation);
        } else {
            m_scaledImage = pixmap;
        }
        
        // Update display widget size
        m_displayWidget->setMinimumSize(m_scaledImage.size());
        m_displayWidget->resize(m_scaledImage.size());
    } else {
        m_isConnected = false;
        m_scaledImage = QPixmap();
    }
    
    update();
}

void RemoteDesktopWidget::setScaleFactor(float scale)
{
    if (scale > 0.1f && scale <= 2.0f) {
        m_scaleFactor = scale;
        
        // Rescale current image if available
        if (!m_desktopImage.isNull()) {
            setDesktopImage(m_desktopImage);
        }
    }
}

void RemoteDesktopWidget::setImageQuality(int quality)
{
    m_imageQuality = qBound(1, quality, 100);
}

void RemoteDesktopWidget::setColorDepth(int depth)
{
    m_colorDepth = depth;
}

void RemoteDesktopWidget::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Fill background
    painter.fillRect(rect(), QColor(26, 26, 26));
    
    if (m_isConnected && !m_scaledImage.isNull()) {
        // Draw the desktop image
        QRect imageRect = calculateImageRect();
        painter.drawPixmap(imageRect, m_scaledImage);
        
        // Draw performance info overlay
        drawPerformanceInfo(painter);
    } else {
        // Draw connection status
        drawConnectionStatus(painter);
    }
}

void RemoteDesktopWidget::drawConnectionStatus(QPainter &painter)
{
    painter.setPen(QColor(170, 170, 170));
    QFont font = painter.font();
    font.setPointSize(16);
    font.setBold(true);
    painter.setFont(font);
    
    QString statusText = m_isConnected ? "Loading desktop..." : "Not connected to remote desktop";
    QFontMetrics fm(font);
    QRect textRect = fm.boundingRect(statusText);
    
    QPoint center = rect().center();
    QPoint textPos(center.x() - textRect.width() / 2, center.y() - textRect.height() / 2);
    
    painter.drawText(textPos, statusText);
    
    // Draw connection icon
    if (!m_isConnected) {
        painter.setPen(QPen(QColor(255, 107, 107), 3));
        painter.setBrush(Qt::NoBrush);
        
        QRect iconRect(center.x() - 30, center.y() - 60, 60, 40);
        painter.drawRoundedRect(iconRect, 5, 5);
        
        // Draw X
        painter.drawLine(iconRect.topLeft() + QPoint(10, 10), 
                        iconRect.bottomRight() - QPoint(10, 10));
        painter.drawLine(iconRect.topRight() + QPoint(-10, 10), 
                        iconRect.bottomLeft() - QPoint(-10, 10));
    }
}

void RemoteDesktopWidget::drawPerformanceInfo(QPainter &painter)
{
    // Draw performance overlay in top-right corner
    painter.setPen(QColor(255, 255, 255, 180));
    QFont font = painter.font();
    font.setPointSize(10);
    painter.setFont(font);
    
    QStringList infoLines = {
        QString("FPS: %1").arg(m_currentFPS),
        QString("Latency: %1ms").arg(m_currentLatency),
        QString("Scale: %1%").arg(int(m_scaleFactor * 100)),
        QString("Quality: %1%").arg(m_imageQuality)
    };
    
    QFontMetrics fm(font);
    int lineHeight = fm.height();
    int maxWidth = 0;
    
    for (const QString &line : infoLines) {
        maxWidth = qMax(maxWidth, fm.horizontalAdvance(line));
    }
    
    // Draw semi-transparent background
    QRect infoRect(width() - maxWidth - INFO_MARGIN * 2, INFO_MARGIN,
                   maxWidth + INFO_MARGIN, infoLines.size() * lineHeight + INFO_MARGIN);
    
    painter.fillRect(infoRect, QColor(0, 0, 0, 120));
    painter.setPen(QColor(100, 100, 100));
    painter.drawRect(infoRect);
    
    // Draw text
    painter.setPen(QColor(255, 255, 255, 200));
    for (int i = 0; i < infoLines.size(); ++i) {
        QPoint textPos(infoRect.x() + INFO_MARGIN / 2, 
                      infoRect.y() + INFO_MARGIN / 2 + (i + 1) * lineHeight - 2);
        painter.drawText(textPos, infoLines[i]);
    }
}

QRect RemoteDesktopWidget::calculateImageRect() const
{
    if (m_scaledImage.isNull()) {
        return QRect();
    }
    
    QSize imageSize = m_scaledImage.size();
    QSize widgetSize = size();
    
    // Center the image in the widget
    int x = (widgetSize.width() - imageSize.width()) / 2;
    int y = (widgetSize.height() - imageSize.height()) / 2;
    
    return QRect(QPoint(qMax(0, x), qMax(0, y)), imageSize);
}

void RemoteDesktopWidget::mousePressEvent(QMouseEvent *event)
{
    m_lastMousePos = event->pos();
    m_mousePressed = true;
    
    // Convert to remote desktop coordinates
    QRect imageRect = calculateImageRect();
    if (imageRect.contains(event->pos())) {
        QPoint relativePos = event->pos() - imageRect.topLeft();
        
        // Scale back to original coordinates if needed
        if (m_scaleFactor != 1.0f) {
            relativePos = QPoint(relativePos.x() / m_scaleFactor, relativePos.y() / m_scaleFactor);
        }
        
        // Create new event with adjusted coordinates
        QMouseEvent adjustedEvent(event->type(), relativePos, event->button(), 
                                event->buttons(), event->modifiers());
        emit mouseEvent(&adjustedEvent);
    }
    
    QWidget::mousePressEvent(event);
}

void RemoteDesktopWidget::mouseMoveEvent(QMouseEvent *event)
{
    if (m_mousePressed) {
        QRect imageRect = calculateImageRect();
        if (imageRect.contains(event->pos())) {
            QPoint relativePos = event->pos() - imageRect.topLeft();
            
            if (m_scaleFactor != 1.0f) {
                relativePos = QPoint(relativePos.x() / m_scaleFactor, relativePos.y() / m_scaleFactor);
            }
            
            QMouseEvent adjustedEvent(event->type(), relativePos, event->button(), 
                                    event->buttons(), event->modifiers());
            emit mouseEvent(&adjustedEvent);
        }
    }
    
    m_lastMousePos = event->pos();
    QWidget::mouseMoveEvent(event);
}

void RemoteDesktopWidget::mouseReleaseEvent(QMouseEvent *event)
{
    m_mousePressed = false;
    
    QRect imageRect = calculateImageRect();
    if (imageRect.contains(event->pos())) {
        QPoint relativePos = event->pos() - imageRect.topLeft();
        
        if (m_scaleFactor != 1.0f) {
            relativePos = QPoint(relativePos.x() / m_scaleFactor, relativePos.y() / m_scaleFactor);
        }
        
        QMouseEvent adjustedEvent(event->type(), relativePos, event->button(), 
                                event->buttons(), event->modifiers());
        emit mouseEvent(&adjustedEvent);
    }
    
    QWidget::mouseReleaseEvent(event);
}

void RemoteDesktopWidget::wheelEvent(QWheelEvent *event)
{
    emit wheelEventSignal(event);
    QWidget::wheelEvent(event);
}

void RemoteDesktopWidget::keyPressEvent(QKeyEvent *event)
{
    emit keyEvent(event);
    QWidget::keyPressEvent(event);
}

void RemoteDesktopWidget::keyReleaseEvent(QKeyEvent *event)
{
    emit keyEvent(event);
    QWidget::keyReleaseEvent(event);
}

void RemoteDesktopWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    update();
}

void RemoteDesktopWidget::updateDesktopImage(const QPixmap &image)
{
    using namespace ModernHVNC;

    try {
        setDesktopImage(image);
        SimpleLogger::Log(LogLevel::Debug, "Desktop image updated: %dx%d", image.width(), image.height());
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception updating desktop image: %s", e.what());
    }
}

void RemoteDesktopWidget::updatePerformanceMetrics(int fps, int latency, float cpu, float memory)
{
    m_fps = fps;
    m_latency = latency;
    m_cpuUsage = cpu;
    m_memoryUsage = memory;

    // Update display if performance overlay is enabled
    if (m_showPerformanceOverlay) {
        update();
    }
}

void RemoteDesktopWidget::togglePerformanceOverlay()
{
    m_showPerformanceOverlay = !m_showPerformanceOverlay;
    update();
}

void RemoteDesktopWidget::updateDisplay()
{
    // This would typically update FPS and other metrics
    // For now, just trigger a repaint if needed
    if (m_isConnected) {
        update();
    }
}
