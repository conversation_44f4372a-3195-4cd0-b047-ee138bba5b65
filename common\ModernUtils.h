#pragma once
#include <memory>
#include <string>
#include <vector>
#include <functional>
#include <shlobj.h>
#include "Common.h"

namespace ModernHVNC {

// C++20 Concepts for type safety
template<typename T>
concept WindowsHandle = std::is_same_v<T, HANDLE> || std::is_same_v<T, HWND> || std::is_same_v<T, HKEY>;

template<typename T>
concept StringLike = std::is_same_v<T, char*> || std::is_same_v<T, const char*> ||
                     std::is_same_v<T, wchar_t*> || std::is_same_v<T, const wchar_t*> ||
                     std::is_same_v<T, std::string> || std::is_same_v<T, std::wstring>;

template<typename T>
concept IntegralType = std::integral<T>;

template<typename T>
concept PointerType = std::is_pointer_v<T>;

// RAII wrapper for Windows handles
class HandleWrapper {
private:
    HANDLE handle_;
    std::function<void(HANDLE)> deleter_;

public:
    explicit HandleWrapper(HANDLE handle = INVALID_HANDLE_VALUE, 
                          std::function<void(HANDLE)> deleter = [](HANDLE h) { 
                              if (h != INVALID_HANDLE_VALUE && h != nullptr) {
                                  CloseHandle(h);
                              }
                          }) noexcept
        : handle_(handle), deleter_(std::move(deleter)) {}

    ~HandleWrapper() noexcept {
        if (handle_ != INVALID_HANDLE_VALUE && handle_ != nullptr) {
            deleter_(handle_);
        }
    }

    // Move constructor
    HandleWrapper(HandleWrapper&& other) noexcept 
        : handle_(other.handle_), deleter_(std::move(other.deleter_)) {
        other.handle_ = INVALID_HANDLE_VALUE;
    }

    // Move assignment
    HandleWrapper& operator=(HandleWrapper&& other) noexcept {
        if (this != &other) {
            if (handle_ != INVALID_HANDLE_VALUE && handle_ != nullptr) {
                deleter_(handle_);
            }
            handle_ = other.handle_;
            deleter_ = std::move(other.deleter_);
            other.handle_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }

    // Delete copy constructor and assignment
    HandleWrapper(const HandleWrapper&) = delete;
    HandleWrapper& operator=(const HandleWrapper&) = delete;

    HANDLE get() const noexcept { return handle_; }
    HANDLE release() noexcept {
        HANDLE temp = handle_;
        handle_ = INVALID_HANDLE_VALUE;
        return temp;
    }

    explicit operator bool() const noexcept {
        return handle_ != INVALID_HANDLE_VALUE && handle_ != nullptr;
    }
};

// Custom deleter for LocalAlloc
struct LocalAllocDeleter {
    void operator()(void* ptr) const noexcept {
        if (ptr) {
            LocalFree(ptr);
        }
    }
};

// Custom deleter for malloc
struct MallocDeleter {
    void operator()(void* ptr) const noexcept {
        if (ptr) {
            free(ptr);
        }
    }
};

// Custom deleter for Funcs::pFree
struct FuncsFreeDeleter {
    void operator()(void* ptr) const noexcept {
        if (ptr) {
            Funcs::pFree(ptr);
        }
    }
};

// Type aliases for smart pointers with custom deleters
template<typename T>
using unique_local_ptr = std::unique_ptr<T, LocalAllocDeleter>;

template<typename T>
using unique_malloc_ptr = std::unique_ptr<T, MallocDeleter>;

template<typename T>
using unique_funcs_ptr = std::unique_ptr<T, FuncsFreeDeleter>;

// Factory functions for creating smart pointers
template<typename T>
unique_local_ptr<T> make_local_unique(size_t size) {
    return unique_local_ptr<T>(static_cast<T*>(LocalAlloc(LPTR, size)));
}

template<typename T>
unique_malloc_ptr<T> make_malloc_unique(size_t size) {
    return unique_malloc_ptr<T>(static_cast<T*>(malloc(size)));
}

template<typename T>
unique_funcs_ptr<T> make_funcs_unique(size_t size) {
    return unique_funcs_ptr<T>(static_cast<T*>(Funcs::pMalloc(size)));
}

// Modern string conversion functions
std::unique_ptr<char[]> Utf16toUtf8Modern(const wchar_t* utf16);
std::unique_ptr<wchar_t[]> Utf8toUtf16Modern(const char* utf8);

// Forward declarations
class FileWrapper;

// Modern string utilities
std::string Utf16toUtf8String(const wchar_t* utf16);
std::wstring Utf8toUtf16String(const char* utf8);
std::string FormatString(const char* format, ...);
std::wstring FormatWString(const wchar_t* format, ...);

// Structured binding utilities
std::pair<DWORD, DWORD> GetFileSize(HANDLE hFile) noexcept;
std::tuple<bool, DWORD, DWORD> GetWindowDimensions(HWND hWnd) noexcept;

public:
    static void Initialize(const char* log_path = "hvnc_client.log") noexcept;
    static void Log(LogLevel level, const char* format, ...) noexcept;
    static void LogAppStart(const char* app_name, const char* path = nullptr) noexcept;
    static void LogAppEnd(const char* app_name, DWORD exit_code = 0) noexcept;
    static void Shutdown() noexcept;

private:
    static const char* GetLogLevelString(LogLevel level) noexcept;
    static std::string GetTimestamp() noexcept;
};

// Fast application launcher with logging
class FastAppLauncher {
public:
    struct AppInfo {
        const char* name;
        const char* display_name;
        std::function<std::string()> path_resolver;
        const char* arguments;
        bool use_profile;
    };

    static bool LaunchApp(const AppInfo& app, const char* desktop_name) noexcept;
    static std::string GetChromePath() noexcept;
    static std::string GetFirefoxPath() noexcept;
    static std::string GetEdgePath() noexcept;
    static std::string GetBravePath() noexcept;
    static std::string GetPowershellPath() noexcept;

private:
    static bool CreateProcessFast(const char* path, const char* args, const char* desktop) noexcept;
    static std::string GetAppDataPath(int csidl) noexcept;
};

// Template functions using C++20 concepts
template<WindowsHandle T>
constexpr bool IsValidHandle(T handle) noexcept {
    if constexpr (std::is_same_v<T, HANDLE>) {
        return handle != INVALID_HANDLE_VALUE && handle != nullptr;
    } else {
        return handle != nullptr;
    }
}

template<IntegralType T>
constexpr T SafeAdd(T a, T b) noexcept {
    // Simple overflow-safe addition without using std::numeric_limits
    // to avoid macro conflicts with Windows headers
    if constexpr (std::is_unsigned_v<T>) {
        return (a > static_cast<T>(~0) - b) ? static_cast<T>(~0) : a + b;
    } else {
        // For signed types, use simple bounds checking
        if (b > 0 && a > static_cast<T>((1ULL << (sizeof(T) * 8 - 1)) - 1) - b) {
            return static_cast<T>((1ULL << (sizeof(T) * 8 - 1)) - 1);
        }
        if (b < 0 && a < static_cast<T>(-(1LL << (sizeof(T) * 8 - 1))) - b) {
            return static_cast<T>(-(1LL << (sizeof(T) * 8 - 1)));
        }
        return a + b;
    }
}

// RAII wrapper for registry keys
class RegistryKeyWrapper {
private:
    HKEY key_;

public:
    explicit RegistryKeyWrapper(HKEY key = nullptr) noexcept : key_(key) {}
    
    ~RegistryKeyWrapper() noexcept {
        if (key_) {
            RegCloseKey(key_);
        }
    }

    // Move constructor
    RegistryKeyWrapper(RegistryKeyWrapper&& other) noexcept : key_(other.key_) {
        other.key_ = nullptr;
    }

    // Move assignment
    RegistryKeyWrapper& operator=(RegistryKeyWrapper&& other) noexcept {
        if (this != &other) {
            if (key_) {
                RegCloseKey(key_);
            }
            key_ = other.key_;
            other.key_ = nullptr;
        }
        return *this;
    }

    // Delete copy constructor and assignment
    RegistryKeyWrapper(const RegistryKeyWrapper&) = delete;
    RegistryKeyWrapper& operator=(const RegistryKeyWrapper&) = delete;

    HKEY get() const noexcept { return key_; }
    HKEY release() noexcept {
        HKEY temp = key_;
        key_ = nullptr;
        return temp;
    }

    explicit operator bool() const noexcept { return key_ != nullptr; }
};

// RAII wrapper for file handles
class FileWrapper {
private:
    HANDLE file_;

public:
    explicit FileWrapper(HANDLE file = INVALID_HANDLE_VALUE) noexcept : file_(file) {}
    
    ~FileWrapper() noexcept {
        if (file_ != INVALID_HANDLE_VALUE) {
            CloseHandle(file_);
        }
    }

    // Move constructor
    FileWrapper(FileWrapper&& other) noexcept : file_(other.file_) {
        other.file_ = INVALID_HANDLE_VALUE;
    }

    // Move assignment
    FileWrapper& operator=(FileWrapper&& other) noexcept {
        if (this != &other) {
            if (file_ != INVALID_HANDLE_VALUE) {
                CloseHandle(file_);
            }
            file_ = other.file_;
            other.file_ = INVALID_HANDLE_VALUE;
        }
        return *this;
    }

    // Delete copy constructor and assignment
    FileWrapper(const FileWrapper&) = delete;
    FileWrapper& operator=(const FileWrapper&) = delete;

    HANDLE get() const noexcept { return file_; }
    HANDLE release() noexcept {
        HANDLE temp = file_;
        file_ = INVALID_HANDLE_VALUE;
        return temp;
    }

    explicit operator bool() const noexcept { return file_ != INVALID_HANDLE_VALUE; }
};

// Modern logging system
enum class LogLevel : int {
    Info = 0,
    Warning = 1,
    Error = 2,
    Debug = 3
};

class Logger {
private:
    static constexpr size_t MAX_LOG_SIZE = 1024;
    static inline bool initialized_ = false;
    static inline FileWrapper log_file_;

public:
    static void Initialize(const char* log_path = "hvnc_client.log") noexcept;
    static void Log(LogLevel level, const char* format, ...) noexcept;
    static void LogAppStart(const char* app_name, const char* path = nullptr) noexcept;
    static void LogAppEnd(const char* app_name, DWORD exit_code = 0) noexcept;
    static void Shutdown() noexcept;

private:
    static const char* GetLogLevelString(LogLevel level) noexcept;
    static std::string GetTimestamp() noexcept;
};

// Modern version info structure with spaceship operator
struct VersionInfo {
    DWORD major;
    DWORD minor;
    DWORD build;
    DWORD revision;

    // C++20 three-way comparison operator
    constexpr auto operator<=>(const VersionInfo& other) const noexcept {
        if (auto cmp = major <=> other.major; cmp != 0) return cmp;
        if (auto cmp = minor <=> other.minor; cmp != 0) return cmp;
        if (auto cmp = build <=> other.build; cmp != 0) return cmp;
        return revision <=> other.revision;
    }

    // Equality operator (automatically generated from <=> in C++20)
    constexpr bool operator==(const VersionInfo& other) const noexcept = default;
};

// Modern rectangle structure with spaceship operator
struct ModernRect {
    LONG left;
    LONG top;
    LONG right;
    LONG bottom;

    constexpr LONG width() const noexcept { return right - left; }
    constexpr LONG height() const noexcept { return bottom - top; }
    constexpr LONG area() const noexcept { return width() * height(); }

    // Three-way comparison based on area
    constexpr auto operator<=>(const ModernRect& other) const noexcept {
        return area() <=> other.area();
    }

    constexpr bool operator==(const ModernRect& other) const noexcept = default;
};

} // namespace ModernHVNC
