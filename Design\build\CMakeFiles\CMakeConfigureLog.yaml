
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 7/14/2025 4:01:39 PM.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.66
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/3.31.7/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-kxljed"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-kxljed"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-kxljed'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_2b1c3.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 4:01:40 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kxljed\\cmTC_2b1c3.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_2b1c3.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kxljed\\Debug\\".
          Creating directory "cmTC_2b1c3.dir\\Debug\\cmTC_2b1c3.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_2b1c3.dir\\Debug\\cmTC_2b1c3.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_2b1c3.dir\\Debug\\cmTC_2b1c3.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_2b1c3.dir\\Debug\\\\" /Fd"cmTC_2b1c3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_2b1c3.dir\\Debug\\\\" /Fd"cmTC_2b1c3.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kxljed\\Debug\\cmTC_2b1c3.exe" /INCREMENTAL /ILK:"cmTC_2b1c3.dir\\Debug\\cmTC_2b1c3.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-kxljed/Debug/cmTC_2b1c3.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-kxljed/Debug/cmTC_2b1c3.lib" /MACHINE:X64  /machine:x64 cmTC_2b1c3.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_2b1c3.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kxljed\\Debug\\cmTC_2b1c3.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_2b1c3.dir\\Debug\\cmTC_2b1c3.tlog\\unsuccessfulbuild".
          Touching "cmTC_2b1c3.dir\\Debug\\cmTC_2b1c3.tlog\\cmTC_2b1c3.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kxljed\\cmTC_2b1c3.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:00.57
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake:24 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:112 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-hmvbhs"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-hmvbhs"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-hmvbhs'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_e0ba7.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 4:01:41 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\cmTC_e0ba7.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_e0ba7.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\Debug\\".
          Creating directory "cmTC_e0ba7.dir\\Debug\\cmTC_e0ba7.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_e0ba7.dir\\Debug\\cmTC_e0ba7.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_e0ba7.dir\\Debug\\cmTC_e0ba7.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_e0ba7.dir\\Debug\\\\" /Fd"cmTC_e0ba7.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_e0ba7.dir\\Debug\\\\" /Fd"cmTC_e0ba7.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\src.cxx"
          src.cxx
        C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\src.cxx(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\cmTC_e0ba7.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\cmTC_e0ba7.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\cmTC_e0ba7.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\src.cxx(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-hmvbhs\\cmTC_e0ba7.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.50
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake:24 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:112 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-yf3qxw"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-yf3qxw"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-yf3qxw'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b686e.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 4:01:41 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\cmTC_b686e.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b686e.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\Debug\\".
          Creating directory "cmTC_b686e.dir\\Debug\\cmTC_b686e.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b686e.dir\\Debug\\cmTC_b686e.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_b686e.dir\\Debug\\cmTC_b686e.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_b686e.dir\\Debug\\\\" /Fd"cmTC_b686e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\CheckFunctionExists.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_b686e.dir\\Debug\\\\" /Fd"cmTC_b686e.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\Debug\\cmTC_b686e.exe" /INCREMENTAL /ILK:"cmTC_b686e.dir\\Debug\\cmTC_b686e.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-yf3qxw/Debug/cmTC_b686e.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-yf3qxw/Debug/cmTC_b686e.lib" /MACHINE:X64  /machine:x64 cmTC_b686e.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\cmTC_b686e.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\cmTC_b686e.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\cmTC_b686e.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-yf3qxw\\cmTC_b686e.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.79
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake:24 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:112 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rxdicf"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rxdicf"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rxdicf'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_c0b3c.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 4:01:43 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\cmTC_c0b3c.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_c0b3c.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\Debug\\".
          Creating directory "cmTC_c0b3c.dir\\Debug\\cmTC_c0b3c.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_c0b3c.dir\\Debug\\cmTC_c0b3c.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_c0b3c.dir\\Debug\\cmTC_c0b3c.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_c0b3c.dir\\Debug\\\\" /Fd"cmTC_c0b3c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\CheckFunctionExists.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_c0b3c.dir\\Debug\\\\" /Fd"cmTC_c0b3c.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\Debug\\cmTC_c0b3c.exe" /INCREMENTAL /ILK:"cmTC_c0b3c.dir\\Debug\\cmTC_c0b3c.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rxdicf/Debug/cmTC_c0b3c.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rxdicf/Debug/cmTC_c0b3c.lib" /MACHINE:X64  /machine:x64 cmTC_c0b3c.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\cmTC_c0b3c.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\cmTC_c0b3c.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\cmTC_c0b3c.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rxdicf\\cmTC_c0b3c.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.61
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:30 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:40 (include)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:157 (find_package)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-11gdfi"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-11gdfi"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-11gdfi'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_95d00.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 4:01:44 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\cmTC_95d00.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_95d00.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\Debug\\".
          Creating directory "cmTC_95d00.dir\\Debug\\cmTC_95d00.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_95d00.dir\\Debug\\cmTC_95d00.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_95d00.dir\\Debug\\cmTC_95d00.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_95d00.dir\\Debug\\\\" /Fd"cmTC_95d00.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_95d00.dir\\Debug\\\\" /Fd"cmTC_95d00.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\Debug\\cmTC_95d00.exe" /INCREMENTAL /ILK:"cmTC_95d00.dir\\Debug\\cmTC_95d00.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-11gdfi/Debug/cmTC_95d00.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-11gdfi/Debug/cmTC_95d00.lib" /MACHINE:X64  /machine:x64 cmTC_95d00.dir\\Debug\\src.obj
          cmTC_95d00.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\Debug\\cmTC_95d00.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_95d00.dir\\Debug\\cmTC_95d00.tlog\\unsuccessfulbuild".
          Touching "cmTC_95d00.dir\\Debug\\cmTC_95d00.tlog\\cmTC_95d00.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-11gdfi\\cmTC_95d00.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.02
        
      exitCode: 0
...
