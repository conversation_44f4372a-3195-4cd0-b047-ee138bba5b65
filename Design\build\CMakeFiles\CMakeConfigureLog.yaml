
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake:205 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
      Build started 7/14/2025 5:28:29 PM.
      
      Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj" on node 1 (default targets).
      PrepareForBuild:
        Creating directory "Debug\\".
        Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
        Creating directory "Debug\\CompilerIdCXX.tlog\\".
      InitializeBuildStatus:
        Creating "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
        Touching "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        Deleting file "Debug\\CompilerIdCXX.tlog\\unsuccessfulbuild".
        Touching "Debug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate".
      Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\3.31.7\\CompilerIdCXX\\CompilerIdCXX.vcxproj" (default targets).
      
      Build succeeded.
          0 Warning(s)
          0 Error(s)
      
      Time Elapsed 00:00:00.71
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/3.31.7/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:74 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1tvwxm"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1tvwxm"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1tvwxm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_951b8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 5:28:30 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1tvwxm\\cmTC_951b8.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_951b8.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1tvwxm\\Debug\\".
          Creating directory "cmTC_951b8.dir\\Debug\\cmTC_951b8.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_951b8.dir\\Debug\\cmTC_951b8.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_951b8.dir\\Debug\\cmTC_951b8.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_951b8.dir\\Debug\\\\" /Fd"cmTC_951b8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_951b8.dir\\Debug\\\\" /Fd"cmTC_951b8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.31\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1tvwxm\\Debug\\cmTC_951b8.exe" /INCREMENTAL /ILK:"cmTC_951b8.dir\\Debug\\cmTC_951b8.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1tvwxm/Debug/cmTC_951b8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1tvwxm/Debug/cmTC_951b8.lib" /MACHINE:X64  /machine:x64 cmTC_951b8.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_951b8.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1tvwxm\\Debug\\cmTC_951b8.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_951b8.dir\\Debug\\cmTC_951b8.tlog\\unsuccessfulbuild".
          Touching "cmTC_951b8.dir\\Debug\\cmTC_951b8.tlog\\cmTC_951b8.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1tvwxm\\cmTC_951b8.vcxproj" (default targets).
        
        Build succeeded.
            0 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.01
        
      exitCode: 0
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:218 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Parsed CXX implicit link information:
        link line regex: [^( *|.*[/\\])(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?|CMAKE_LINK_STARTFILE-NOTFOUND|([^/\\]+-)?ld|collect2)[^/\\]*( |$)]
        linker tool regex: [^[ 	]*(->|")?[ 	]*(([^"]*[/\\])?(ld[0-9]*(\\.[a-z]+)?|link\\.exe|lld-link(\\.exe)?))("|,| |$)]
        linker tool for 'CXX': C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe
        implicit libs: []
        implicit objs: []
        implicit dirs: []
        implicit fwks: []
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake:40 (message)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake:255 (cmake_determine_linker_id)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Running the CXX compiler's linker: "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/bin/HostX64/x64/link.exe" "-v"
      Microsoft (R) Incremental Linker Version 14.44.35207.1
      Copyright (C) Microsoft Corporation.  All rights reserved.
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake:24 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:112 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1ygofj"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1ygofj"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-1ygofj'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6277a.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 5:28:31 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\cmTC_6277a.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6277a.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\Debug\\".
          Creating directory "cmTC_6277a.dir\\Debug\\cmTC_6277a.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6277a.dir\\Debug\\cmTC_6277a.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6277a.dir\\Debug\\cmTC_6277a.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6277a.dir\\Debug\\\\" /Fd"cmTC_6277a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6277a.dir\\Debug\\\\" /Fd"cmTC_6277a.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\src.cxx"
          src.cxx
        C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\src.cxx(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\cmTC_6277a.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\cmTC_6277a.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\cmTC_6277a.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\src.cxx(1,10): error C1083: Cannot open include file: 'pthread.h': No such file or directory [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-1ygofj\\cmTC_6277a.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.72
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake:24 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:112 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-j5ak4q"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-j5ak4q"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-j5ak4q'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_7e04d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 5:28:32 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\cmTC_7e04d.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_7e04d.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\Debug\\".
          Creating directory "cmTC_7e04d.dir\\Debug\\cmTC_7e04d.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_7e04d.dir\\Debug\\cmTC_7e04d.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_7e04d.dir\\Debug\\cmTC_7e04d.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_7e04d.dir\\Debug\\\\" /Fd"cmTC_7e04d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\CheckFunctionExists.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_7e04d.dir\\Debug\\\\" /Fd"cmTC_7e04d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\Debug\\cmTC_7e04d.exe" /INCREMENTAL /ILK:"cmTC_7e04d.dir\\Debug\\cmTC_7e04d.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-j5ak4q/Debug/cmTC_7e04d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-j5ak4q/Debug/cmTC_7e04d.lib" /MACHINE:X64  /machine:x64 cmTC_7e04d.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\cmTC_7e04d.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\cmTC_7e04d.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\cmTC_7e04d.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthreads.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-j5ak4q\\cmTC_7e04d.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.83
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake:78 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake:24 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:112 (include)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rztgrf"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rztgrf"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rztgrf'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6e367.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 5:28:34 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\cmTC_6e367.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6e367.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\Debug\\".
          Creating directory "cmTC_6e367.dir\\Debug\\cmTC_6e367.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6e367.dir\\Debug\\cmTC_6e367.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6e367.dir\\Debug\\cmTC_6e367.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6e367.dir\\Debug\\\\" /Fd"cmTC_6e367.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\CheckFunctionExists.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6e367.dir\\Debug\\\\" /Fd"cmTC_6e367.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\Debug\\cmTC_6e367.exe" /INCREMENTAL /ILK:"cmTC_6e367.dir\\Debug\\cmTC_6e367.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rztgrf/Debug/cmTC_6e367.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rztgrf/Debug/cmTC_6e367.lib" /MACHINE:X64  /machine:x64 cmTC_6e367.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\cmTC_6e367.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\cmTC_6e367.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\cmTC_6e367.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : fatal error LNK1104: cannot open file 'pthread.lib' [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rztgrf\\cmTC_6e367.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.71
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:30 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:40 (include)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:157 (find_package)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rwgd3y"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rwgd3y"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-rwgd3y'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_6b0b1.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 5:28:35 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_6b0b1.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\Debug\\".
          Creating directory "cmTC_6b0b1.dir\\Debug\\cmTC_6b0b1.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_6b0b1.dir\\Debug\\cmTC_6b0b1.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_6b0b1.dir\\Debug\\cmTC_6b0b1.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6b0b1.dir\\Debug\\\\" /Fd"cmTC_6b0b1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_6b0b1.dir\\Debug\\\\" /Fd"cmTC_6b0b1.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\src.cxx"
          src.cxx
        C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\src.cxx(1,1): error C1090: PDB API call failed, error code '3': C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.dir\\Debug\\vc143.pdb [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.vcxproj]
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.vcxproj" (default targets) -- FAILED.
        
        Build FAILED.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.vcxproj" (default target) (1) ->
        (ClCompile target) -> 
          C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\src.cxx(1,1): error C1090: PDB API call failed, error code '3': C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.dir\\Debug\\vc143.pdb [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rwgd3y\\cmTC_6b0b1.vcxproj]
        
            0 Warning(s)
            1 Error(s)
        
        Time Elapsed 00:00:00.78
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake:108 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake:58 (cmake_check_source_compiles)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/FindWrapAtomic.cmake:41 (check_cxx_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake:30 (_qt_internal_find_third_party_dependencies)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake:40 (include)"
      - "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake:157 (find_package)"
      - "CMakeLists.txt:9 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC_WITH_LIB"
    directories:
      source: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-k74vnm"
      binary: "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-k74vnm"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/3rdparty/kwin"
    buildResult:
      variable: "HAVE_STDATOMIC_WITH_LIB"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-k74vnm'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_b64f4.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        MSBuild version 17.14.8+a7a4d5af0 for .NET Framework
        Build started 7/14/2025 5:28:36 PM.
        
        Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\cmTC_b64f4.vcxproj" on node 1 (default targets).
        PrepareForBuild:
          Creating directory "cmTC_b64f4.dir\\Debug\\".
          Structured output is enabled. The formatting of compiler diagnostics will reflect the error hierarchy. See https://aka.ms/cpp/structured-output for more details.
          Creating directory "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\Debug\\".
          Creating directory "cmTC_b64f4.dir\\Debug\\cmTC_b64f4.tlog\\".
        InitializeBuildStatus:
          Creating "cmTC_b64f4.dir\\Debug\\cmTC_b64f4.tlog\\unsuccessfulbuild" because "AlwaysCreate" was specified.
          Touching "cmTC_b64f4.dir\\Debug\\cmTC_b64f4.tlog\\unsuccessfulbuild".
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC_WITH_LIB /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_b64f4.dir\\Debug\\\\" /Fd"cmTC_b64f4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\src.cxx"
          Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
          Copyright (C) Microsoft Corporation.  All rights reserved.
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC_WITH_LIB /D "CMAKE_INTDIR=\\"Debug\\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++20 /Fo"cmTC_b64f4.dir\\Debug\\\\" /Fd"cmTC_b64f4.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\Debug\\cmTC_b64f4.exe" /INCREMENTAL /ILK:"cmTC_b64f4.dir\\Debug\\cmTC_b64f4.ilk" /NOLOGO "-latomic" kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-k74vnm/Debug/cmTC_b64f4.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/CMakeScratch/TryCompile-k74vnm/Debug/cmTC_b64f4.lib" /MACHINE:X64  /machine:x64 cmTC_b64f4.dir\\Debug\\src.obj
        LINK : warning LNK4044: unrecognized option '/latomic'; ignored [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\cmTC_b64f4.vcxproj]
          cmTC_b64f4.vcxproj -> C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\Debug\\cmTC_b64f4.exe
        FinalizeBuildStatus:
          Deleting file "cmTC_b64f4.dir\\Debug\\cmTC_b64f4.tlog\\unsuccessfulbuild".
          Touching "cmTC_b64f4.dir\\Debug\\cmTC_b64f4.tlog\\cmTC_b64f4.lastbuildstate".
        Done Building Project "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\cmTC_b64f4.vcxproj" (default targets).
        
        Build succeeded.
        
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\cmTC_b64f4.vcxproj" (default target) (1) ->
        (Link target) -> 
          LINK : warning LNK4044: unrecognized option '/latomic'; ignored [C:\\Users\\<USER>\\OneDrive\\Desktop\\HVNC\\Design\\build\\CMakeFiles\\CMakeScratch\\TryCompile-k74vnm\\cmTC_b64f4.vcxproj]
        
            1 Warning(s)
            0 Error(s)
        
        Time Elapsed 00:00:01.60
        
      exitCode: 0
...
