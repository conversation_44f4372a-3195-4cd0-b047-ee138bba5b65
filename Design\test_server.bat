@echo off
echo Testing HVNC Server Integration
echo ================================

echo.
echo 1. Checking if Server.exe exists in build\Release...
if exist "build\Release\Server.exe" (
    echo   ✓ Server.exe found in build\Release
) else (
    echo   ✗ Server.exe NOT found in build\Release
)

echo.
echo 2. Checking if Server.exe exists in ..\Server\build...
if exist "..\Server\build\Server.exe" (
    echo   ✓ Server.exe found in ..\Server\build
) else (
    echo   ✗ Server.exe NOT found in ..\Server\build
)

echo.
echo 3. Testing Server.exe execution...
cd build\Release
if exist "Server.exe" (
    echo   Starting Server.exe on port 8080...
    start /B Server.exe 8080
    timeout /t 2 >nul
    echo   ✓ Server started (check Task Manager for Server.exe process)
    
    echo.
    echo 4. Testing Qt GUI connection to server...
    echo   Starting HVNC Controller...
    start "HVNC Controller" "HVNC Controller.exe"
    
    echo.
    echo   Instructions:
    echo   1. Click "START SERVER" button in the GUI
    echo   2. Set port to 8080
    echo   3. Click "CONNECT" button
    echo   4. Try launching applications
    
) else (
    echo   ✗ Server.exe not found in build\Release
)

echo.
echo Press any key to continue...
pause >nul
