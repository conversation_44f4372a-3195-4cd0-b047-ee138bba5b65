#include <QApplication>
#include <QMainWindow>
#include <QLabel>
#include <QVBoxLayout>
#include <QWidget>
#include <QDebug>
#include <QMessageBox>
#include <QTimer>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Test application starting...";
    qDebug() << "Qt version:" << QT_VERSION_STR;
    
    try {
        // Create a simple test window
        QMainWindow *window = new QMainWindow();
        window->setWindowTitle("HVNC Controller - Test Mode");
        window->resize(800, 600);
        
        // Create central widget
        QWidget *central = new QWidget();
        window->setCentralWidget(central);
        
        // Create layout
        QVBoxLayout *layout = new QVBoxLayout(central);
        
        // Add test labels
        QLabel *titleLabel = new QLabel("HVNC Controller Test");
        titleLabel->setStyleSheet("font-size: 24px; color: #00ff00; font-weight: bold; background-color: #000000; padding: 20px;");
        layout->addWidget(titleLabel);
        
        QLabel *statusLabel = new QLabel("Application is running successfully!");
        statusLabel->setStyleSheet("font-size: 16px; color: #00ff00; background-color: #000000; padding: 10px;");
        layout->addWidget(statusLabel);
        
        QLabel *instructionLabel = new QLabel("If you can see this, the basic Qt6 setup is working.\nThe issue is likely in the MainWindow or SplashScreen classes.");
        instructionLabel->setStyleSheet("font-size: 12px; color: #00ff00; background-color: #000000; padding: 10px;");
        layout->addWidget(instructionLabel);
        
        // Set dark background
        window->setStyleSheet("QMainWindow { background-color: #000000; }");
        
        qDebug() << "Test window created, showing...";
        window->show();
        
        // Auto-close after 10 seconds for testing
        QTimer::singleShot(10000, [window]() {
            qDebug() << "Test completed, closing window...";
            window->close();
        });
        
        qDebug() << "Entering event loop...";
        int result = app.exec();
        
        qDebug() << "Application exiting with code:" << result;
        delete window;
        return result;
    }
    catch (const std::exception& e) {
        qDebug() << "Exception in test main:" << e.what();
        QMessageBox::critical(nullptr, "Test Error", 
            QString("Test application failed: %1").arg(e.what()));
        return -1;
    }
    catch (...) {
        qDebug() << "Unknown exception in test main";
        QMessageBox::critical(nullptr, "Test Error", 
            "Test application failed due to unknown error");
        return -1;
    }
}
