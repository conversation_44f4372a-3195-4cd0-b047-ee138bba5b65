#include "ModernUtils.h"
#include <stdarg.h>
#include <vector>

namespace ModernHVNC {

std::unique_ptr<char[]> Utf16toUtf8Modern(const wchar_t* utf16) {
    if (!utf16) {
        return nullptr;
    }
    
    const int strLen = Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
    if (!strLen) {
        return nullptr;
    }
    
    auto result = std::make_unique<char[]>(strLen + 1);
    if (!result) {
        return nullptr;
    }
    
    Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, result.get(), strLen, nullptr, nullptr);
    return result;
}

std::unique_ptr<wchar_t[]> Utf8toUtf16Modern(const char* utf8) {
    if (!utf8) {
        return nullptr;
    }
    
    const int strLen = Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
    if (!strLen) {
        return nullptr;
    }
    
    auto result = std::make_unique<wchar_t[]>(strLen + 1);
    if (!result) {
        return nullptr;
    }
    
    Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, result.get(), strLen);
    return result;
}

std::string Utf16toUtf8String(const wchar_t* utf16) {
    if (!utf16) {
        return {};
    }

    const int strLen = Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
    if (!strLen) {
        return {};
    }

    std::string result(strLen - 1, '\0'); // -1 to exclude null terminator
    Funcs::pWideCharToMultiByte(CP_UTF8, 0, utf16, -1, result.data(), strLen, nullptr, nullptr);
    return result;
}

std::wstring Utf8toUtf16String(const char* utf8) {
    if (!utf8) {
        return {};
    }

    const int strLen = Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
    if (!strLen) {
        return {};
    }

    std::wstring result(strLen - 1, L'\0'); // -1 to exclude null terminator
    Funcs::pMultiByteToWideChar(CP_UTF8, 0, utf8, -1, result.data(), strLen);
    return result;
}

std::string FormatString(const char* format, ...) {
    va_list args;
    va_start(args, format);

    // Get required size
    const int size = _vscprintf(format, args) + 1;
    va_end(args);

    if (size <= 1) {
        return {};
    }

    std::string result(size - 1, '\0');
    va_start(args, format);
    vsprintf_s(result.data(), size, format, args);
    va_end(args);

    return result;
}

std::wstring FormatWString(const wchar_t* format, ...) {
    va_list args;
    va_start(args, format);

    // Get required size
    const int size = _vscwprintf(format, args) + 1;
    va_end(args);

    if (size <= 1) {
        return {};
    }

    std::wstring result(size - 1, L'\0');
    va_start(args, format);
    vswprintf_s(result.data(), size, format, args);
    va_end(args);

    return result;
}

} // namespace ModernHVNC
