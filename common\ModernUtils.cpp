#include "ModernUtils.h"
#include <stdarg.h>
#include <vector>
#include <ctime>
#include <chrono>

namespace ModernHVNC {

std::unique_ptr<char[]> Utf16toUtf8Modern(const wchar_t* utf16) {
    if (!utf16) {
        return nullptr;
    }
    
    const int strLen = WideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
    if (!strLen) {
        return nullptr;
    }
    
    auto result = std::make_unique<char[]>(strLen + 1);
    if (!result) {
        return nullptr;
    }
    
    WideCharToMultiByte(CP_UTF8, 0, utf16, -1, result.get(), strLen, nullptr, nullptr);
    return result;
}

std::unique_ptr<wchar_t[]> Utf8toUtf16Modern(const char* utf8) {
    if (!utf8) {
        return nullptr;
    }
    
    const int strLen = MultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
    if (!strLen) {
        return nullptr;
    }
    
    auto result = std::make_unique<wchar_t[]>(strLen + 1);
    if (!result) {
        return nullptr;
    }
    
    MultiByteToWideChar(CP_UTF8, 0, utf8, -1, result.get(), strLen);
    return result;
}

std::string Utf16toUtf8String(const wchar_t* utf16) {
    if (!utf16) {
        return {};
    }

    const int strLen = WideCharToMultiByte(CP_UTF8, 0, utf16, -1, nullptr, 0, nullptr, nullptr);
    if (!strLen) {
        return {};
    }

    std::string result(strLen - 1, '\0'); // -1 to exclude null terminator
    WideCharToMultiByte(CP_UTF8, 0, utf16, -1, result.data(), strLen, nullptr, nullptr);
    return result;
}

std::wstring Utf8toUtf16String(const char* utf8) {
    if (!utf8) {
        return {};
    }

    const int strLen = MultiByteToWideChar(CP_UTF8, 0, utf8, -1, nullptr, 0);
    if (!strLen) {
        return {};
    }

    std::wstring result(strLen - 1, L'\0'); // -1 to exclude null terminator
    MultiByteToWideChar(CP_UTF8, 0, utf8, -1, result.data(), strLen);
    return result;
}

std::string FormatString(const char* format, ...) {
    va_list args;
    va_start(args, format);

    // Get required size
    const int size = _vscprintf(format, args) + 1;
    va_end(args);

    if (size <= 1) {
        return {};
    }

    std::string result(size - 1, '\0');
    va_start(args, format);
    vsprintf_s(result.data(), size, format, args);
    va_end(args);

    return result;
}

std::wstring FormatWString(const wchar_t* format, ...) {
    va_list args;
    va_start(args, format);

    // Get required size
    const int size = _vscwprintf(format, args) + 1;
    va_end(args);

    if (size <= 1) {
        return {};
    }

    std::wstring result(size - 1, L'\0');
    va_start(args, format);
    vswprintf_s(result.data(), size, format, args);
    va_end(args);

    return result;
}

std::pair<DWORD, DWORD> GetFileSize(HANDLE hFile) noexcept {
    DWORD fileSizeLow = 0;
    DWORD fileSizeHigh = 0;
    fileSizeLow = ::GetFileSize(hFile, &fileSizeHigh);
    return {fileSizeLow, fileSizeHigh};
}

std::tuple<bool, DWORD, DWORD> GetWindowDimensions(HWND hWnd) noexcept {
    RECT rect;
    const bool success = ::GetWindowRect(hWnd, &rect) != FALSE;
    const DWORD width = success ? static_cast<DWORD>(rect.right - rect.left) : 0;
    const DWORD height = success ? static_cast<DWORD>(rect.bottom - rect.top) : 0;
    return {success, width, height};
}

// Logger implementation

void Logger::Initialize(const char* log_path) noexcept {
    if (initialized_) return;

    try {
        HANDLE hFile = CreateFileA(
            log_path,
            GENERIC_WRITE,
            FILE_SHARE_READ,
            nullptr,
            CREATE_ALWAYS,
            FILE_ATTRIBUTE_NORMAL,
            nullptr
        );

        if (hFile != INVALID_HANDLE_VALUE) {
            log_file_ = FileWrapper(hFile);
            initialized_ = true;
            Log(LogLevel::Info, "HVNC Client logging initialized");
        }
    } catch (...) {
        // Silent failure for logging initialization
    }
}

void Logger::Log(LogLevel level, const char* format, ...) noexcept {
    if (!initialized_ || !log_file_) return;

    try {
        char buffer[MAX_LOG_SIZE];
        va_list args;
        va_start(args, format);

        const auto timestamp = GetTimestamp();
        const auto level_str = GetLogLevelString(level);

        int written = _snprintf_s(buffer, sizeof(buffer), _TRUNCATE,
            "[%s] [%s] ", timestamp.c_str(), level_str);

        if (written > 0 && written < static_cast<int>(sizeof(buffer))) {
            written += _vsnprintf_s(buffer + written, sizeof(buffer) - written,
                _TRUNCATE, format, args);
        }

        va_end(args);

        if (written > 0) {
            // Add newline
            if (written < static_cast<int>(sizeof(buffer)) - 2) {
                buffer[written++] = '\r';
                buffer[written++] = '\n';
            }

            DWORD bytes_written;
            WriteFile(log_file_.get(), buffer, written, &bytes_written, nullptr);
            FlushFileBuffers(log_file_.get());
        }
    } catch (...) {
        // Silent failure for logging
    }
}

void Logger::LogAppStart(const char* app_name, const char* path) noexcept {
    if (path) {
        Log(LogLevel::Info, "Starting application: %s (Path: %s)", app_name, path);
    } else {
        Log(LogLevel::Info, "Starting application: %s", app_name);
    }
}

void Logger::LogAppEnd(const char* app_name, DWORD exit_code) noexcept {
    Log(LogLevel::Info, "Application ended: %s (Exit code: %lu)", app_name, exit_code);
}

void Logger::Shutdown() noexcept {
    if (initialized_) {
        Log(LogLevel::Info, "HVNC Client logging shutdown");
        log_file_ = FileWrapper(); // Reset to close file
        initialized_ = false;
    }
}

const char* Logger::GetLogLevelString(LogLevel level) noexcept {
    switch (level) {
        case LogLevel::Info: return "INFO";
        case LogLevel::Warning: return "WARN";
        case LogLevel::Error: return "ERROR";
        case LogLevel::Debug: return "DEBUG";
        default: return "UNKNOWN";
    }
}

std::string Logger::GetTimestamp() noexcept {
    try {
        SYSTEMTIME st;
        GetLocalTime(&st);

        char timestamp[64];
        _snprintf_s(timestamp, sizeof(timestamp), _TRUNCATE,
            "%04d-%02d-%02d %02d:%02d:%02d.%03d",
            st.wYear, st.wMonth, st.wDay,
            st.wHour, st.wMinute, st.wSecond, st.wMilliseconds);

        return std::string(timestamp);
    } catch (...) {
        return "0000-00-00 00:00:00.000";
    }
}

// FastAppLauncher implementation
bool FastAppLauncher::LaunchApp(const AppInfo& app, const char* desktop_name) noexcept {
    try {
        Logger::LogAppStart(app.display_name);

        const auto path = app.path_resolver();
        if (path.empty()) {
            Logger::Log(LogLevel::Error, "Failed to resolve path for %s", app.display_name);
            return false;
        }

        Logger::Log(LogLevel::Debug, "Resolved path for %s: %s", app.display_name, path.c_str());

        const bool success = CreateProcessFast(path.c_str(), app.arguments, desktop_name);
        if (success) {
            Logger::Log(LogLevel::Info, "Successfully launched %s", app.display_name);
        } else {
            Logger::Log(LogLevel::Error, "Failed to launch %s", app.display_name);
        }

        return success;
    } catch (...) {
        Logger::Log(LogLevel::Error, "Exception while launching %s", app.display_name);
        return false;
    }
}

bool FastAppLauncher::CreateProcessFast(const char* path, const char* args, const char* desktop) noexcept {
    try {
        STARTUPINFOA si = {};
        si.cb = sizeof(si);
        si.lpDesktop = const_cast<char*>(desktop);

        PROCESS_INFORMATION pi = {};

        // Create command line
        std::string cmdline;
        if (args && strlen(args) > 0) {
            cmdline = std::string("\"") + path + "\" " + args;
        } else {
            cmdline = std::string("\"") + path + "\"";
        }

        const BOOL result = CreateProcessA(
            nullptr,
            const_cast<char*>(cmdline.c_str()),
            nullptr,
            nullptr,
            FALSE,
            CREATE_NEW_CONSOLE,
            nullptr,
            nullptr,
            &si,
            &pi
        );

        if (result) {
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            return true;
        }

        return false;
    } catch (...) {
        return false;
    }
}

std::string FastAppLauncher::GetAppDataPath(int csidl) noexcept {
    try {
        char path[MAX_PATH] = {};
        if (SUCCEEDED(SHGetFolderPathA(nullptr, csidl, nullptr, 0, path))) {
            return std::string(path);
        }
    } catch (...) {
        // Fall through to empty string
    }
    return {};
}

std::string FastAppLauncher::GetChromePath() noexcept {
    try {
        // Try multiple common Chrome locations
        const std::vector<std::string> chrome_paths = {
            GetAppDataPath(CSIDL_LOCAL_APPDATA) + "\\Google\\Chrome\\Application\\chrome.exe",
            GetAppDataPath(CSIDL_PROGRAM_FILES) + "\\Google\\Chrome\\Application\\chrome.exe",
            GetAppDataPath(CSIDL_PROGRAM_FILESX86) + "\\Google\\Chrome\\Application\\chrome.exe"
        };

        for (const auto& path : chrome_paths) {
            if (GetFileAttributesA(path.c_str()) != INVALID_FILE_ATTRIBUTES) {
                return path;
            }
        }
    } catch (...) {
        // Fall through
    }
    return {};
}

std::string FastAppLauncher::GetFirefoxPath() noexcept {
    try {
        const std::vector<std::string> firefox_paths = {
            GetAppDataPath(CSIDL_PROGRAM_FILES) + "\\Mozilla Firefox\\firefox.exe",
            GetAppDataPath(CSIDL_PROGRAM_FILESX86) + "\\Mozilla Firefox\\firefox.exe"
        };

        for (const auto& path : firefox_paths) {
            if (GetFileAttributesA(path.c_str()) != INVALID_FILE_ATTRIBUTES) {
                return path;
            }
        }
    } catch (...) {
        // Fall through
    }
    return {};
}

std::string FastAppLauncher::GetEdgePath() noexcept {
    try {
        const std::vector<std::string> edge_paths = {
            GetAppDataPath(CSIDL_PROGRAM_FILES) + "\\Microsoft\\Edge\\Application\\msedge.exe",
            GetAppDataPath(CSIDL_PROGRAM_FILESX86) + "\\Microsoft\\Edge\\Application\\msedge.exe"
        };

        for (const auto& path : edge_paths) {
            if (GetFileAttributesA(path.c_str()) != INVALID_FILE_ATTRIBUTES) {
                return path;
            }
        }
    } catch (...) {
        // Fall through
    }
    return {};
}

std::string FastAppLauncher::GetBravePath() noexcept {
    try {
        const std::vector<std::string> brave_paths = {
            GetAppDataPath(CSIDL_LOCAL_APPDATA) + "\\BraveSoftware\\Brave-Browser\\Application\\brave.exe",
            GetAppDataPath(CSIDL_PROGRAM_FILES) + "\\BraveSoftware\\Brave-Browser\\Application\\brave.exe"
        };

        for (const auto& path : brave_paths) {
            if (GetFileAttributesA(path.c_str()) != INVALID_FILE_ATTRIBUTES) {
                return path;
            }
        }
    } catch (...) {
        // Fall through
    }
    return {};
}

std::string FastAppLauncher::GetPowershellPath() noexcept {
    try {
        const std::vector<std::string> ps_paths = {
            "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe",
            "C:\\Windows\\SysWOW64\\WindowsPowerShell\\v1.0\\powershell.exe"
        };

        for (const auto& path : ps_paths) {
            if (GetFileAttributesA(path.c_str()) != INVALID_FILE_ATTRIBUTES) {
                return path;
            }
        }
    } catch (...) {
        // Fall through
    }
    return {};
}

} // namespace ModernHVNC
