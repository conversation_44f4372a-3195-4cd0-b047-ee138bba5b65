#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QTimer>
#include <QSplashScreen>
#include <QPixmap>
#include <QPainter>
#include <QFont>
#include <QProgressBar>
#include <QVBoxLayout>
#include <QLabel>
#include <QWidget>
#include <QMessageBox>
#include <QDebug>

#include "MainWindow.h"
#include "SplashScreen.h"

void setupDarkTheme(QApplication& app) {
    // Set dark theme with Fusion style for better control
    app.setStyle(QStyleFactory::create("Fusion"));

    // Hacker-style dark palette - sharp blacks and greens
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(15, 15, 15));           // Almost black
    darkPalette.setColor(QPalette::WindowText, QColor(0, 255, 0));        // Bright green text
    darkPalette.setColor(QPalette::Base, QColor(8, 8, 8));                // Pure black base
    darkPalette.setColor(QPalette::AlternateBase, QColor(20, 20, 20));    // Dark gray alternate
    darkPalette.setColor(QPalette::ToolTipBase, QColor(0, 0, 0));         // Black tooltips
    darkPalette.setColor(QPalette::ToolTipText, QColor(0, 255, 0));       // Green tooltip text
    darkPalette.setColor(QPalette::Text, QColor(0, 255, 0));              // Bright green text
    darkPalette.setColor(QPalette::Button, QColor(25, 25, 25));           // Dark button
    darkPalette.setColor(QPalette::ButtonText, QColor(0, 255, 0));        // Green button text
    darkPalette.setColor(QPalette::BrightText, QColor(255, 0, 0));        // Red for errors
    darkPalette.setColor(QPalette::Link, QColor(0, 200, 255));            // Cyan links
    darkPalette.setColor(QPalette::Highlight, QColor(0, 150, 0));         // Dark green highlight
    darkPalette.setColor(QPalette::HighlightedText, QColor(0, 0, 0));     // Black highlighted text

    app.setPalette(darkPalette);
    
    // Apply hacker-style sharp dark theme
    app.setStyleSheet(R"(
        QMainWindow {
            background-color: #0f0f0f;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QPushButton {
            background-color: #1a1a1a;
            border: 1px solid #00ff00;
            border-radius: 2px;
            padding: 8px 16px;
            color: #00ff00;
            font-weight: bold;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            text-transform: uppercase;
        }

        QPushButton:hover {
            background-color: #003300;
            border-color: #00ff00;
            color: #ffffff;
            box-shadow: 0 0 10px #00ff00;
        }

        QPushButton:pressed {
            background-color: #001100;
            border-color: #00cc00;
        }
        
        QFrame {
            background-color: #0f0f0f;
            border: 1px solid #003300;
            border-radius: 0px;
        }

        QGroupBox {
            font-weight: bold;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            border: 1px solid #00ff00;
            border-radius: 0px;
            margin-top: 1ex;
            padding-top: 15px;
            color: #00ff00;
            background-color: #0a0a0a;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 8px 0 8px;
            color: #00ff00;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        QSlider::groove:horizontal {
            border: 1px solid #00ff00;
            height: 6px;
            background: #000000;
            border-radius: 0px;
        }

        QSlider::handle:horizontal {
            background: #00ff00;
            border: 1px solid #00ff00;
            width: 16px;
            margin: -3px 0;
            border-radius: 0px;
        }

        QComboBox {
            border: 1px solid #00ff00;
            border-radius: 0px;
            padding: 6px 12px;
            background-color: #1a1a1a;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QComboBox::drop-down {
            border: none;
            width: 20px;
        }

        QComboBox::down-arrow {
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid #00ff00;
        }

        QComboBox QAbstractItemView {
            background-color: #0f0f0f;
            border: 1px solid #00ff00;
            color: #00ff00;
            selection-background-color: #003300;
        }
        
        QLineEdit {
            border: 1px solid #00ff00;
            border-radius: 0px;
            padding: 8px;
            background-color: #000000;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QLineEdit:focus {
            border-color: #00ff00;
            background-color: #001100;
            box-shadow: 0 0 5px #00ff00;
        }

        QStatusBar {
            background-color: #0f0f0f;
            border-top: 1px solid #00ff00;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QMenuBar {
            background-color: #0f0f0f;
            border-bottom: 1px solid #00ff00;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QMenuBar::item {
            padding: 6px 12px;
            background-color: transparent;
        }

        QMenuBar::item:selected {
            background-color: #003300;
            border: 1px solid #00ff00;
        }

        QMenu {
            background-color: #0f0f0f;
            border: 1px solid #00ff00;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QMenu::item {
            padding: 8px 24px;
            background-color: transparent;
        }

        QMenu::item:selected {
            background-color: #003300;
            color: #ffffff;
        }

        QLabel {
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QTextEdit {
            background-color: #000000;
            border: 1px solid #00ff00;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            border-radius: 0px;
        }

        QProgressBar {
            border: 1px solid #00ff00;
            border-radius: 0px;
            background-color: #000000;
            text-align: center;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        QProgressBar::chunk {
            background-color: #00ff00;
            border-radius: 0px;
        }
    )");
}

int main(int argc, char *argv[])
{
    // Enable high DPI scaling
    QApplication::setHighDpiScaleFactorRoundingPolicy(Qt::HighDpiScaleFactorRoundingPolicy::PassThrough);

    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("HVNC Controller");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("HVNC Systems");

    qDebug() << "HVNC Controller starting...";
    qDebug() << "Qt version:" << QT_VERSION_STR;

    try {
        // Setup dark theme
        qDebug() << "Setting up dark theme...";
        setupDarkTheme(app);

        qDebug() << "Creating main window...";
        // Create main window first (without showing)
        MainWindow *mainWindow = new MainWindow();

        qDebug() << "Creating splash screen...";
        // Create and show splash screen
        SplashScreen *splash = new SplashScreen();
        splash->show();

        // Process events to show splash screen
        app.processEvents();

        qDebug() << "Connecting splash to main window...";
        // Connect splash screen finished signal to show main window
        QObject::connect(splash, &SplashScreen::finished, [mainWindow, splash]() {
            qDebug() << "Splash finished, showing main window...";
            mainWindow->show();
            mainWindow->raise();
            mainWindow->activateWindow();
            splash->deleteLater();
        });

        // Start splash screen animation
        qDebug() << "Starting splash animation...";
        splash->startAnimation();

        qDebug() << "Entering event loop...";
        int result = app.exec();

        qDebug() << "Application exiting with code:" << result;
        delete mainWindow;
        return result;
    }
    catch (const std::exception& e) {
        qDebug() << "Exception in main:" << e.what();
        QMessageBox::critical(nullptr, "Fatal Error",
            QString("Application failed to start: %1").arg(e.what()));
        return -1;
    }
    catch (...) {
        qDebug() << "Unknown exception in main";
        QMessageBox::critical(nullptr, "Fatal Error",
            "Application failed to start due to unknown error");
        return -1;
    }
}
