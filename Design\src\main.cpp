#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QTimer>
#include <QSplashScreen>
#include <QPixmap>
#include <QPainter>
#include <QFont>
#include <QProgressBar>
#include <QVBoxLayout>
#include <QLabel>
#include <QWidget>

#include "MainWindow.h"
#include "SplashScreen.h"

void setupDarkTheme(QApplication& app) {
    // Set dark theme
    app.setStyle(QStyleFactory::create("Fusion"));
    
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    
    app.setPalette(darkPalette);
    
    // Apply custom stylesheet for modern look
    app.setStyleSheet(R"(
        QMainWindow {
            background-color: #2b2b2b;
        }
        
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px 16px;
            color: white;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #4a4a4a;
            border-color: #666666;
        }
        
        QPushButton:pressed {
            background-color: #363636;
        }
        
        QFrame {
            border-radius: 4px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 6px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        
        QSlider::groove:horizontal {
            border: 1px solid #555555;
            height: 8px;
            background: #404040;
            border-radius: 4px;
        }
        
        QSlider::handle:horizontal {
            background: #2a82da;
            border: 1px solid #555555;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }
        
        QComboBox {
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 4px 8px;
            background-color: #404040;
        }
        
        QComboBox::drop-down {
            border: none;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid white;
        }
        
        QLineEdit {
            border: 1px solid #555555;
            border-radius: 4px;
            padding: 6px;
            background-color: #404040;
        }
        
        QLineEdit:focus {
            border-color: #2a82da;
        }
        
        QStatusBar {
            background-color: #353535;
            border-top: 1px solid #555555;
        }
        
        QMenuBar {
            background-color: #353535;
            border-bottom: 1px solid #555555;
        }
        
        QMenuBar::item {
            padding: 4px 8px;
        }
        
        QMenuBar::item:selected {
            background-color: #2a82da;
        }
        
        QMenu {
            background-color: #404040;
            border: 1px solid #555555;
        }
        
        QMenu::item {
            padding: 6px 20px;
        }
        
        QMenu::item:selected {
            background-color: #2a82da;
        }
    )");
}

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Set application properties
    app.setApplicationName("HVNC Controller");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("HVNC Systems");
    
    // Setup dark theme
    setupDarkTheme(app);
    
    // Create and show splash screen
    SplashScreen splash;
    splash.show();
    
    // Process events to show splash screen
    app.processEvents();
    
    // Create main window but don't show it yet
    MainWindow mainWindow;
    
    // Connect splash screen finished signal to show main window
    QObject::connect(&splash, &SplashScreen::finished, [&mainWindow]() {
        mainWindow.show();
    });
    
    // Start splash screen animation
    splash.startAnimation();
    
    return app.exec();
}
