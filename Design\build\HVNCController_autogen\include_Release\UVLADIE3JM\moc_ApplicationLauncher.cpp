/****************************************************************************
** Meta object code from reading C++ file 'ApplicationLauncher.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../src/ApplicationLauncher.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ApplicationLauncher.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSApplicationLauncherENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSApplicationLauncherENDCLASS = QtMocHelpers::stringData(
    "ApplicationLauncher",
    "applicationLaunched",
    "",
    "appName",
    "pid",
    "launchChrome",
    "launchFirefox",
    "launchEdge",
    "launchBrave",
    "launchPowerShell",
    "launchFileExplorer"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSApplicationLauncherENDCLASS_t {
    uint offsetsAndSizes[22];
    char stringdata0[20];
    char stringdata1[20];
    char stringdata2[1];
    char stringdata3[8];
    char stringdata4[4];
    char stringdata5[13];
    char stringdata6[14];
    char stringdata7[11];
    char stringdata8[12];
    char stringdata9[17];
    char stringdata10[19];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSApplicationLauncherENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSApplicationLauncherENDCLASS_t qt_meta_stringdata_CLASSApplicationLauncherENDCLASS = {
    {
        QT_MOC_LITERAL(0, 19),  // "ApplicationLauncher"
        QT_MOC_LITERAL(20, 19),  // "applicationLaunched"
        QT_MOC_LITERAL(40, 0),  // ""
        QT_MOC_LITERAL(41, 7),  // "appName"
        QT_MOC_LITERAL(49, 3),  // "pid"
        QT_MOC_LITERAL(53, 12),  // "launchChrome"
        QT_MOC_LITERAL(66, 13),  // "launchFirefox"
        QT_MOC_LITERAL(80, 10),  // "launchEdge"
        QT_MOC_LITERAL(91, 11),  // "launchBrave"
        QT_MOC_LITERAL(103, 16),  // "launchPowerShell"
        QT_MOC_LITERAL(120, 18)   // "launchFileExplorer"
    },
    "ApplicationLauncher",
    "applicationLaunched",
    "",
    "appName",
    "pid",
    "launchChrome",
    "launchFirefox",
    "launchEdge",
    "launchBrave",
    "launchPowerShell",
    "launchFileExplorer"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSApplicationLauncherENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
       7,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       1,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    2,   56,    2, 0x06,    1 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       5,    0,   61,    2, 0x08,    4 /* Private */,
       6,    0,   62,    2, 0x08,    5 /* Private */,
       7,    0,   63,    2, 0x08,    6 /* Private */,
       8,    0,   64,    2, 0x08,    7 /* Private */,
       9,    0,   65,    2, 0x08,    8 /* Private */,
      10,    0,   66,    2, 0x08,    9 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::Int,    3,    4,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ApplicationLauncher::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSApplicationLauncherENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSApplicationLauncherENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSApplicationLauncherENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ApplicationLauncher, std::true_type>,
        // method 'applicationLaunched'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'launchChrome'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'launchFirefox'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'launchEdge'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'launchBrave'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'launchPowerShell'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'launchFileExplorer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ApplicationLauncher::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ApplicationLauncher *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->applicationLaunched((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 1: _t->launchChrome(); break;
        case 2: _t->launchFirefox(); break;
        case 3: _t->launchEdge(); break;
        case 4: _t->launchBrave(); break;
        case 5: _t->launchPowerShell(); break;
        case 6: _t->launchFileExplorer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ApplicationLauncher::*)(const QString & , int );
            if (_t _q_method = &ApplicationLauncher::applicationLaunched; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
    }
}

const QMetaObject *ApplicationLauncher::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ApplicationLauncher::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSApplicationLauncherENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ApplicationLauncher::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 7)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 7;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 7)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 7;
    }
    return _id;
}

// SIGNAL 0
void ApplicationLauncher::applicationLaunched(const QString & _t1, int _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}
QT_WARNING_POP
