﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{EECB5F4D-5553-3289-BB45-98BD119D2915}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ALL_BUILD</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\common;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\..\Client;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/OneDrive/Desktop/HVNC/Design -BC:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build --check-stamp-file C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXCompilerABI.cpp;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeCompilerIdDetection.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerABI.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineCompilerSupport.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeDetermineSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindBinUtils.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitIncludeInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseImplicitLinkInfo.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeParseLibraryArchitecture.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCCompiler.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystem.cmake.in;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCXXCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestCompilerCommon.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CMakeTestRCCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ADSP-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMCC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\ARMClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\AppleClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Borland-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Clang-DetermineCompilerInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Compaq-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Cray-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\CrayClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Embarcadero-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Fujitsu-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\FujitsuClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GHS-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\GNU-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\HP-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IAR-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMCPP-CXX-DetermineVersionInternal.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IBMClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Intel-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\IntelLLVM-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\LCC-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVHPC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\NVIDIA-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OpenWatcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\OrangeC-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PGI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\PathScale-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SCO-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\SunPro-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TI-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\TIClang-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Tasking-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\VisualAge-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\Watcom-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XL-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\XLClang-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Compiler\zOS-CXX-DetermineCompiler.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\CompilerId\VS-10.vcxproj.in;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\FindVulkan.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCXXLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeCommonLinkerInformation.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeDetermineLinkerId.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CMakeInspectCXXLinker.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Internal\FeatureTesting.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Linker\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Determine-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.31\Modules\Platform\WindowsPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapAtomic.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\FindWrapVulkanHeaders.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Config.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6ConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Dependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6Targets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\Qt6VersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeature.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtFeatureCommon.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicAppleHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicDependencyHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFinalizerHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicFindPackageHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicPluginHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTargetHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicTestHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicToolHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6\QtPublicWalkLibsHelpers.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigExtras.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\Qt6CoreVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Core\QtInstallPaths.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6GuiVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QPdfPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgIconPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QSvgPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6NetworkVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QNLMNIPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QSchannelBackendPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendCertOnlyPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Network\Qt6QTlsBackendOpenSSLPluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6QWindowsVistaStylePluginTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6Widgets\Qt6WidgetsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets-relwithdebinfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateAdditionalTargetInfo.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfig.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersion.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateConfigVersionImpl.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateTargets.cmake;C:\Qt\6.5.3\msvc2019_64\lib\cmake\Qt6ZlibPrivate\Qt6ZlibPrivateVersionlessTargets.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeCXXCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeRCCompiler.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\3.31.7\CMakeSystem.cmake;C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\resources\resources.qrc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\ZERO_CHECK.vcxproj">
      <Project>{FB37A46C-E3C3-365F-9A0B-228A61234086}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
    <ProjectReference Include="C:\Users\<USER>\OneDrive\Desktop\HVNC\Design\build\HVNCController.vcxproj">
      <Project>{A0A54B79-3AC1-359C-B07F-8A52975938B5}</Project>
      <Name>HVNCController</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>