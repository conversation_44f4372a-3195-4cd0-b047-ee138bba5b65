#pragma once

#include <QObject>
#include <QTimer>
#include <QString>

class HVNCClient;

class ConnectionManager : public QObject
{
    Q_OBJECT

public:
    explicit ConnectionManager(QObject *parent = nullptr);
    ~ConnectionManager();

    void setConnectionSettings(const QString &host, int port, int timeout, bool autoReconnect);
    void connectToServer();
    void disconnectFromServer();
    
    bool isConnected() const;
    bool isAutoReconnectEnabled() const;
    
    QString getLastError() const;
    int getConnectionAttempts() const;

signals:
    void connectionStateChanged(bool connected);
    void connectionError(const QString &error);
    void reconnectAttempt(int attempt);

private slots:
    void onClientConnected();
    void onClientDisconnected();
    void onClientError(const QString &error);
    void attemptReconnect();

private:
    void resetConnectionState();
    void startReconnectTimer();
    void stopReconnectTimer();
    
    HVNCClient *m_client;
    QTimer *m_reconnectTimer;
    
    QString m_serverHost;
    int m_serverPort;
    int m_connectionTimeout;
    bool m_autoReconnect;
    
    bool m_isConnected;
    bool m_isConnecting;
    bool m_userDisconnected;
    
    QString m_lastError;
    int m_connectionAttempts;
    int m_maxReconnectAttempts;
    int m_reconnectDelay;
    
    static constexpr int DEFAULT_PORT = 4444;
    static constexpr int DEFAULT_TIMEOUT = 5000;
    static constexpr int MAX_RECONNECT_ATTEMPTS = 10;
    static constexpr int INITIAL_RECONNECT_DELAY = 2000; // 2 seconds
    static constexpr int MAX_RECONNECT_DELAY = 30000; // 30 seconds
};
