/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 6.5.3
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/firefox.png
  0x0,0x0,0x0,0x8f,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x56,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0xfc,0x3f,0x95,0xe1,0x3f,
  0x3,0xd,0x1,0x13,0x2d,0xd,0x7,0x81,0x51,0xb,0x8,0x82,0x51,0xb,0x8,0x82,
  0x51,0xb,0xa8,0x64,0x81,0x6a,0x4,0x3,0x43,0xd6,0x7f,0x4,0x36,0x6f,0x61,0x20,
  0x16,0xb0,0x10,0xad,0xf2,0x70,0x2e,0x3,0xc3,0xe5,0x29,0xc,0xb4,0xb,0x22,0xdb,
  0xc9,0x8,0x1f,0xf0,0xc8,0xc,0x45,0x1f,0x90,0x9,0x68,0x6e,0x1,0xe3,0x68,0x85,
  0x43,0x8,0x8c,0x5a,0x40,0x10,0xd0,0xdc,0x2,0x0,0xc4,0x6d,0x13,0xc4,0x2c,0xdb,
  0x70,0xb9,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/hvnc_icon.png
  0x0,0x0,0x0,0x98,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x5f,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0xd4,0x6a,0xba,0xf5,0x9f,
  0x81,0x86,0x80,0x89,0x96,0x86,0x83,0xc0,0xa8,0x5,0x4,0xc1,0xa8,0x5,0x4,0xc1,
  0xa8,0x5,0xd4,0xb1,0xc0,0x57,0x97,0x97,0x21,0xdc,0x98,0x1f,0xce,0x6f,0xf3,0x13,
  0x67,0x90,0x13,0x62,0x1d,0x1c,0x3e,0x60,0x21,0x56,0x61,0x9d,0x97,0x18,0x18,0xc3,
  0xc0,0x8c,0x23,0xef,0xa8,0x6b,0x41,0xd3,0xb6,0x57,0xc,0x2b,0xcf,0x7e,0x84,0x7,
  0xd1,0xd0,0x8a,0x64,0x4a,0x0,0xe3,0x68,0x85,0x43,0x8,0x8c,0x5a,0x40,0x10,0xd0,
  0xdc,0x2,0x0,0xcd,0x79,0x10,0xf7,0xc2,0xf7,0xd4,0xdf,0x0,0x0,0x0,0x0,0x49,
  0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/disconnect.png
  0x0,0x0,0x0,0xc2,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x89,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0xfc,0xe2,0x6c,0xf6,0x9f,
  0x81,0x86,0x80,0x89,0x96,0x86,0x83,0xc0,0xa8,0x5,0x4,0xc1,0xa8,0x5,0x4,0xc1,
  0xa8,0x5,0xd4,0xb1,0x80,0x49,0x4a,0x86,0x81,0x35,0x2c,0x86,0x81,0x81,0x85,0x85,
  0x81,0xbd,0xb4,0x96,0x81,0x14,0xc0,0x42,0x8c,0xa2,0xff,0xdf,0xbf,0x31,0xb0,0x6,
  0x86,0x33,0x30,0xeb,0xe8,0x33,0xfc,0xbd,0x78,0x8e,0xfa,0x3e,0xf8,0xff,0xfe,0x1d,
  0xc3,0xef,0x75,0x2b,0x18,0x98,0x2d,0x6d,0x19,0xfe,0xec,0xda,0x4a,0x7d,0xb,0x18,
  0x85,0x84,0x19,0x58,0x83,0x23,0x19,0xfe,0x1e,0x3f,0xcc,0xc0,0xe2,0xe6,0x4d,0x3,
  0xb,0x38,0xb9,0xc0,0x3e,0xf8,0xd1,0x54,0xc9,0xc0,0xa4,0xa4,0x42,0x92,0x5,0x8c,
  0xa3,0xf5,0x1,0x21,0x30,0x6a,0x1,0x41,0x30,0xf4,0x2d,0x0,0x0,0xb8,0xbe,0x1e,
  0x30,0xa0,0x26,0x9e,0x8a,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,
  0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/settings.png
  0x0,0x0,0x0,0x93,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x5a,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0x6c,0x68,0x68,0xf8,0xcf,
  0x40,0x43,0xc0,0x44,0x4b,0xc3,0x41,0x60,0xd4,0x2,0x82,0x60,0xd4,0x2,0x82,0x60,
  0xd4,0x2,0x82,0x80,0x85,0x81,0x48,0x50,0x5f,0x5f,0x8f,0xc2,0x6f,0x6c,0x6c,0xa4,
  0xae,0x5,0x13,0x27,0x4e,0x64,0xf8,0xf0,0xe1,0x3,0x98,0x2d,0x20,0x20,0x30,0x82,
  0xe2,0x80,0x89,0xd6,0x16,0xb0,0x10,0xab,0x30,0x3f,0x3f,0x9f,0xb6,0x91,0xdc,0x48,
  0xa4,0x81,0xc3,0x2f,0xe,0x98,0x46,0x2d,0x20,0x4,0x68,0x6e,0x1,0x0,0x28,0xab,
  0xe,0xbd,0x8a,0x5b,0x5d,0xef,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,
  0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/edge.png
  0x0,0x0,0x0,0xb1,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x78,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0x64,0xa8,0xb8,0xfe,0x9f,
  0x81,0x86,0x80,0x89,0x96,0x86,0x83,0xc0,0xa8,0x5,0x4,0xc1,0xa8,0x5,0x4,0xc1,
  0xa8,0x5,0x4,0x1,0xb,0x61,0x25,0xc,0xc,0x31,0x86,0x7c,0xc,0x8b,0xc3,0xa4,
  0xe0,0xfc,0x93,0x8f,0xbf,0x33,0x58,0x4c,0x7b,0x48,0x3d,0xb,0x40,0x20,0x75,0xdd,
  0xb,0x86,0x39,0xa7,0x3f,0x30,0xd0,0x2c,0x88,0x66,0x7,0x49,0x30,0xfc,0x6f,0xd7,
  0x0,0xe3,0x9,0x3e,0xe2,0x44,0x5b,0x30,0x78,0x7c,0x40,0x2e,0x60,0x21,0x25,0x88,
  0x66,0x7,0x49,0x80,0xd9,0x2f,0xbf,0xfc,0x61,0x90,0x68,0xbd,0x43,0x94,0x3e,0xc6,
  0xd1,0xa,0x87,0x10,0x18,0xb5,0x80,0x20,0xa0,0xb9,0x5,0x0,0x20,0xe4,0x1f,0x27,
  0xb0,0xef,0x2f,0xa6,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
  
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/powershell.png
  0x0,0x0,0x1,0x3b,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x1,0x2,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0x64,0x54,0x9,0xfb,0xcf,
  0x40,0x43,0xc0,0x44,0x4b,0xc3,0x41,0x60,0xd4,0x2,0x82,0x60,0xd4,0x2,0x82,0x60,
  0xd4,0x2,0xca,0x2c,0x48,0x8,0x72,0x60,0xf8,0x77,0x7b,0x25,0x18,0xbf,0x3d,0x33,
  0x8f,0x21,0x31,0xd8,0x81,0x81,0x8f,0x87,0x93,0x61,0xe3,0xcc,0x32,0x86,0x8f,0x17,
  0x16,0x30,0x1c,0x5e,0xd1,0xc4,0x20,0x2f,0x2d,0x4a,0xbe,0x5,0x20,0x90,0x54,0x3e,
  0x9d,0x81,0x49,0x35,0x9c,0x41,0xd7,0xab,0x98,0xa1,0x28,0xd9,0x97,0xc1,0xcb,0xc1,
  0x90,0xe1,0xc2,0xf5,0x7,0xc,0xa2,0xa6,0x29,0xc,0xcd,0x53,0xd6,0x30,0xd4,0xe6,
  0x4,0x53,0x66,0x1,0xc,0xb0,0xb2,0xb2,0x30,0xfc,0xf9,0xfb,0x97,0xe1,0xf0,0xe9,
  0x1b,0xc,0x46,0xda,0x8a,0xc,0xa5,0xa9,0x7e,0xc,0xdf,0x7f,0xfc,0x62,0x48,0xad,
  0x9a,0xc9,0x80,0xf,0x30,0xe2,0x2b,0x4d,0x41,0x41,0x34,0xaf,0x33,0x13,0xcc,0x7e,
  0xfe,0xfa,0x3d,0x43,0x71,0xdb,0x22,0x86,0x15,0x5b,0x8e,0x31,0x30,0x31,0x31,0x32,
  0x18,0x6a,0x29,0x32,0xb8,0xdb,0xea,0x33,0x48,0x8a,0x9,0x32,0xe4,0x36,0xce,0xc3,
  0x69,0x1,0xb,0x31,0x41,0xb4,0x60,0xdd,0x1,0x38,0xbf,0xa3,0x2c,0x9a,0xe1,0xe9,
  0x8b,0xb7,0xc,0xf3,0x56,0xef,0x67,0x78,0xff,0xf1,0x2b,0xc3,0xd2,0xfe,0x3c,0xea,
  0xa6,0xa2,0xce,0x99,0x1b,0x18,0x1c,0x2d,0x74,0x18,0x9e,0x1c,0x9d,0xc1,0xb0,0x63,
  0x7e,0x15,0x43,0xd3,0xe4,0xd5,0xe4,0x7,0x11,0x35,0xc0,0x30,0xcf,0x68,0xd4,0x0,
  0xa3,0x16,0x30,0x10,0x2,0x0,0xba,0xfd,0x4d,0x12,0xd6,0x2f,0x88,0xbf,0x0,0x0,
  0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/connect.png
  0x0,0x0,0x0,0xbc,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x83,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0xf4,0x59,0x1f,0xf0,0x9f,
  0x81,0x86,0x80,0x89,0x96,0x86,0x83,0xc0,0xa8,0x5,0x4,0xc1,0xa8,0x5,0x4,0xc1,
  0xa8,0x5,0x4,0xc1,0xa8,0x5,0xb4,0xb1,0x80,0x83,0x99,0x83,0x41,0x94,0x53,0x94,
  0x28,0xb5,0x2c,0x84,0x14,0x48,0xf3,0x48,0x31,0xf0,0xb0,0xf1,0x60,0x88,0x7,0xa9,
  0x4,0x30,0x6c,0xb8,0xb3,0x89,0xe1,0xfa,0xbb,0x1b,0x94,0x59,0xf0,0xeb,0xef,0x6f,
  0x86,0x1f,0x7f,0x7e,0x60,0x88,0xff,0xf9,0xf7,0x87,0xe1,0xc7,0x5f,0x4c,0x71,0x74,
  0xc0,0x48,0x4e,0x85,0xc3,0xc1,0xc2,0xc1,0xc0,0xc5,0xc2,0xc9,0xf0,0xee,0xc7,0x7b,
  0x82,0x6a,0x9,0xfa,0x0,0x1b,0x0,0xf9,0x8,0x9b,0xaf,0x86,0x4e,0x2a,0x22,0x5,
  0x8c,0x5a,0xc0,0x40,0x8,0x0,0x0,0x76,0xc6,0x1f,0xa8,0x5e,0xb7,0x55,0x81,0x0,
  0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/brave.png
  0x0,0x0,0x0,0xd3,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0x9a,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0xfc,0x1d,0xa2,0xfd,0x9f,
  0x81,0x86,0x80,0x89,0x96,0x86,0x83,0xc0,0xa8,0x5,0x4,0xc1,0xa8,0x5,0x4,0xc1,
  0xa8,0x5,0xd4,0xb1,0x80,0xc9,0x2d,0x8c,0x81,0x65,0xf5,0x15,0x8,0x9e,0x73,0x90,
  0x81,0xd1,0xd0,0x96,0x81,0x58,0xc0,0x42,0xac,0xc2,0xbf,0x13,0x4a,0x19,0xfe,0x1f,
  0xdb,0xc1,0xc0,0x68,0xee,0xca,0xc0,0x28,0x2c,0xce,0xf0,0x9f,0xda,0x41,0xc4,0x5c,
  0xd0,0xcd,0xc0,0xb2,0xea,0x32,0x3,0x73,0x51,0xf,0x3,0x3,0x2b,0x3b,0xb1,0xda,
  0x88,0xb7,0xe0,0xef,0x84,0x52,0x86,0x3f,0xa1,0x3a,0xc,0x7f,0x8b,0x83,0x18,0x18,
  0x5d,0x43,0xa9,0x6f,0x1,0x1c,0xfc,0xf9,0xcd,0xc0,0xf0,0xf1,0x1d,0x3,0xd5,0xe3,
  0x80,0xb9,0xa0,0x9b,0x81,0x1,0x84,0x5f,0x3f,0x63,0xf8,0x3b,0xad,0x96,0x68,0xb,
  0x18,0x47,0x2b,0x1c,0x42,0x60,0xd4,0x2,0x82,0x80,0xe6,0x16,0x0,0x0,0xd9,0x21,
  0x23,0xd7,0xe0,0x52,0x47,0x41,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,
  0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/explorer.png
  0x0,0x0,0x0,0xf6,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0xbd,0x49,0x44,0x41,0x54,0x78,0x9c,0xed,0xd5,0xa1,0xa,0xc2,0x40,
  0x0,0x80,0xe1,0xff,0x8e,0xb1,0x15,0xa3,0xc,0x86,0xcd,0x20,0x36,0x9f,0xc0,0x87,
  0x30,0x1a,0x5,0x8b,0xcf,0xa0,0xc5,0xe2,0x13,0x88,0x46,0x9f,0xc2,0x68,0x30,0x1b,
  0xac,0x62,0x99,0x65,0x20,0x82,0x20,0x2c,0x4d,0x71,0x93,0x63,0x41,0x59,0xd8,0x85,
  0xed,0x82,0x70,0x1f,0x5c,0x38,0xe,0xee,0xe7,0x38,0x8e,0x13,0xd9,0xde,0xcb,0x30,
  0x48,0x9a,0xdc,0x5c,0xb1,0x1,0x2d,0x1b,0xd0,0xb2,0x1,0x2d,0xa7,0x74,0xd5,0x1f,
  0x42,0x77,0xf3,0x9d,0xc7,0x7,0x38,0xf6,0x6b,0xc,0x28,0xe7,0x9,0x5c,0x7f,0x22,
  0xed,0x5,0xdc,0xb7,0x90,0x26,0xd0,0x1c,0x40,0x38,0xa5,0x5a,0xa0,0xb3,0xce,0x87,
  0x12,0x2d,0xe1,0x32,0xcf,0x23,0x59,0xa,0xe1,0xcc,0xc0,0x9,0x94,0xf4,0x5,0xc2,
  0x81,0x77,0x4c,0xf5,0x40,0x91,0x1b,0x40,0xa3,0x7,0xd2,0x3,0xaf,0x5,0x49,0x44,
  0x19,0x51,0xfa,0x1f,0xf8,0x85,0x4b,0x7e,0xde,0xe0,0xb1,0x83,0x68,0x5,0xd2,0x85,
  0x60,0xc,0xa7,0x51,0x85,0x40,0xd,0xfe,0xff,0xa1,0x49,0x1b,0xd0,0x31,0x1e,0xf8,
  0x0,0x97,0x7a,0x2e,0xc3,0x0,0xdc,0xfb,0x30,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,
  0x44,0xae,0x42,0x60,0x82,
    // C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/icons/chrome.png
  0x0,0x0,0x0,0xf8,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x18,0x0,0x0,0x0,0x18,0x8,0x6,0x0,0x0,0x0,0xe0,0x77,0x3d,0xf8,
  0x0,0x0,0x0,0xbf,0x49,0x44,0x41,0x54,0x78,0x9c,0x63,0x74,0x6a,0xfd,0xf2,0x9f,
  0x81,0x86,0x80,0x89,0x96,0x86,0x83,0xc0,0xa8,0x5,0x4,0xc1,0xa8,0x5,0x4,0xc1,
  0xa8,0x5,0x4,0x1,0xb,0x3,0x91,0x20,0xd4,0x9c,0x95,0x21,0xcc,0x82,0x95,0x81,
  0x95,0x99,0x81,0x61,0xd9,0xd1,0xdf,0xc,0xab,0x4e,0xfe,0xa6,0x9e,0x5,0xc6,0x8a,
  0xcc,0xc,0x36,0xea,0xcc,0xc,0x99,0xf3,0xbe,0x83,0xf9,0xc5,0x5e,0xec,0xc,0xa7,
  0xee,0xfd,0x65,0x78,0xf0,0xfa,0x1f,0x75,0x2c,0x30,0x51,0x62,0x66,0x58,0x74,0xf8,
  0x37,0xc3,0x9b,0xcf,0x90,0x72,0xb1,0x72,0xe5,0xf,0x6,0xaa,0xc6,0xc1,0xff,0xff,
  0xc,0xc,0xcc,0x48,0x2a,0x5,0xb8,0x18,0x19,0x78,0x38,0x18,0xa9,0x67,0xc1,0x99,
  0xfb,0x7f,0x19,0xa2,0xad,0x59,0x19,0x24,0xf8,0x19,0x19,0x4,0xb8,0x19,0x19,0x2a,
  0xfd,0xd9,0x19,0x14,0x45,0x89,0x4b,0x1f,0x44,0x5,0xd1,0xb9,0xfb,0x7f,0x19,0xb4,
  0xa5,0x99,0x18,0xa6,0x27,0x71,0x32,0x30,0x32,0x32,0x30,0xac,0x3e,0xf9,0x9b,0xe1,
  0xf2,0xe3,0xbf,0x44,0x59,0xc0,0x38,0x5a,0xe1,0x10,0x2,0xa3,0x16,0x10,0x4,0x34,
  0xb7,0x0,0x0,0x39,0x80,0x2c,0x22,0x32,0xa1,0xbe,0x86,0x0,0x0,0x0,0x0,0x49,
  0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // firefox.png
  0x0,0xb,
  0xd,0xb1,0x46,0x27,
  0x0,0x66,
  0x0,0x69,0x0,0x72,0x0,0x65,0x0,0x66,0x0,0x6f,0x0,0x78,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // hvnc_icon.png
  0x0,0xd,
  0xa,0x8,0x7d,0x7,
  0x0,0x68,
  0x0,0x76,0x0,0x6e,0x0,0x63,0x0,0x5f,0x0,0x69,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // disconnect.png
  0x0,0xe,
  0xa,0x93,0x87,0x7,
  0x0,0x64,
  0x0,0x69,0x0,0x73,0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x6e,0x0,0x65,0x0,0x63,0x0,0x74,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // settings.png
  0x0,0xc,
  0xb,0xdf,0x21,0x47,
  0x0,0x73,
  0x0,0x65,0x0,0x74,0x0,0x74,0x0,0x69,0x0,0x6e,0x0,0x67,0x0,0x73,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // edge.png
  0x0,0x8,
  0xa,0xd8,0x5a,0x27,
  0x0,0x65,
  0x0,0x64,0x0,0x67,0x0,0x65,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // powershell.png
  0x0,0xe,
  0x9,0x96,0x79,0x47,
  0x0,0x70,
  0x0,0x6f,0x0,0x77,0x0,0x65,0x0,0x72,0x0,0x73,0x0,0x68,0x0,0x65,0x0,0x6c,0x0,0x6c,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // connect.png
  0x0,0xb,
  0xb,0x73,0x9d,0xc7,
  0x0,0x63,
  0x0,0x6f,0x0,0x6e,0x0,0x6e,0x0,0x65,0x0,0x63,0x0,0x74,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // brave.png
  0x0,0x9,
  0x8,0xc8,0x84,0x47,
  0x0,0x62,
  0x0,0x72,0x0,0x61,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // explorer.png
  0x0,0xc,
  0x5,0x5b,0xb1,0x87,
  0x0,0x65,
  0x0,0x78,0x0,0x70,0x0,0x6c,0x0,0x6f,0x0,0x72,0x0,0x65,0x0,0x72,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // chrome.png
  0x0,0xa,
  0x6,0x3d,0x68,0x67,
  0x0,0x63,
  0x0,0x68,0x0,0x72,0x0,0x6f,0x0,0x6d,0x0,0x65,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0xa,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/explorer.png
  0x0,0x0,0x0,0xf8,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x6,0x17,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xf2,
  // :/icons/chrome.png
  0x0,0x0,0x1,0x16,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0x11,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xcf,
  // :/icons/brave.png
  0x0,0x0,0x0,0xe0,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0x40,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xe4,
  // :/icons/powershell.png
  0x0,0x0,0x0,0xa2,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x3,0x41,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xed,
  // :/icons/hvnc_icon.png
  0x0,0x0,0x0,0x2c,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x93,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xcb,
  // :/icons/disconnect.png
  0x0,0x0,0x0,0x4c,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x1,0x2f,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8f,0x2,
  // :/icons/edge.png
  0x0,0x0,0x0,0x8c,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x2,0x8c,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xe1,
  // :/icons/connect.png
  0x0,0x0,0x0,0xc4,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x4,0x80,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xfe,
  // :/icons/settings.png
  0x0,0x0,0x0,0x6e,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x1,0xf5,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xf9,
  // :/icons/firefox.png
  0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x98,0x8,0xfc,0x8e,0xdb,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

#ifdef __clang__
#   pragma clang diagnostic push
#   pragma clang diagnostic ignored "-Wexit-time-destructors"
#endif

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_resources)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_resources)(); }
   } dummy;
}

#ifdef __clang__
#   pragma clang diagnostic pop
#endif
