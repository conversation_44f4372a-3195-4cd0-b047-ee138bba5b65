#pragma once

#include <QWidget>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QPainter>
#include <QPixmap>
#include <QTimer>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QWheelEvent>
#include <QResizeEvent>
#include <QPaintEvent>
#include <QScrollArea>
#include <QFrame>

// Include HVNC headers
#include "../common/SimpleLogger.h"

class RemoteDesktopWidget : public QWidget
{
    Q_OBJECT

public:
    explicit RemoteDesktopWidget(QWidget *parent = nullptr);
    ~RemoteDesktopWidget();

    void setDesktopImage(const QPixmap &pixmap);
    void setScaleFactor(float scale);
    void setImageQuality(int quality);
    void setColorDepth(int depth);

public slots:
    void updateDesktopImage(const QPixmap &image);
    void updatePerformanceMetrics(int fps, int latency, float cpu, float memory);
    void togglePerformanceOverlay();

signals:
    void mouseEvent(QMouseEvent *event);
    void keyEvent(QKeyEvent *event);
    void wheelEventSignal(QWheelEvent *event);

protected:
    void paintEvent(QPaintEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void keyReleaseEvent(QKeyEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;

private slots:
    void updateDisplay();

private:
    void setupUI();
    void drawConnectionStatus(QPainter &painter);
    void drawPerformanceInfo(QPainter &painter);
    QRect calculateImageRect() const;
    
    QVBoxLayout *m_mainLayout;
    QScrollArea *m_scrollArea;
    QWidget *m_displayWidget;
    
    QPixmap m_desktopImage;
    QPixmap m_scaledImage;
    
    QTimer *m_updateTimer;
    
    float m_scaleFactor;
    int m_imageQuality;
    int m_colorDepth;
    bool m_isConnected;
    
    // Performance metrics for display
    int m_currentFPS;
    int m_currentLatency;
    
    // Mouse tracking
    QPoint m_lastMousePos;
    bool m_mousePressed;
    
    static constexpr int UPDATE_INTERVAL = 16; // ~60 FPS
    static constexpr int INFO_MARGIN = 10;
};
