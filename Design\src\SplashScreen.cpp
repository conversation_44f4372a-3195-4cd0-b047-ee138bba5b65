#include "SplashScreen.h"
#include <QApplication>
#include <QShowEvent>
#include <QScreen>
#include <QPainter>
#include <QLinearGradient>
#include <QRadialGradient>
#include <QFontDatabase>

SplashScreen::SplashScreen(QWidget *parent)
    : QWidget(parent, Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint)
    , m_mainLayout(nullptr)
    , m_logoLayout(nullptr)
    , m_logo<PERSON>abel(nullptr)
    , m_titleLabel(nullptr)
    , m_versionLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_progressTimer(new QTimer(this))
    , m_fadeAnimation(nullptr)
    , m_opacityEffect(nullptr)
    , m_currentProgress(0)
    , m_messageIndex(0)
{
    setFixedSize(SPLASH_WIDTH, SPLASH_HEIGHT);
    setAttribute(Qt::WA_TranslucentBackground);
    
    // Center on screen
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - SPLASH_WIDTH) / 2;
    int y = (screenGeometry.height() - SPLASH_HEIGHT) / 2;
    move(x, y);
    
    // Setup loading messages
    m_loadingMessages = {
        "Initializing HVNC Controller...",
        "Loading network components...",
        "Setting up remote desktop interface...",
        "Preparing application launchers...",
        "Finalizing startup..."
    };
    
    setupUI();
    
    // Setup progress timer
    connect(m_progressTimer, &QTimer::timeout, this, &SplashScreen::updateProgress);
}

SplashScreen::~SplashScreen() = default;

void SplashScreen::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(30, 30, 30, 30);
    m_mainLayout->setSpacing(20);
    
    // Logo section
    createLogo();
    
    // Title
    m_titleLabel = new QLabel("HVNC Controller", this);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = m_titleLabel->font();
    titleFont.setPointSize(24);
    titleFont.setBold(true);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setStyleSheet("color: white; margin: 10px 0;");
    
    // Version
    m_versionLabel = new QLabel("Version 1.0.0", this);
    m_versionLabel->setAlignment(Qt::AlignCenter);
    QFont versionFont = m_versionLabel->font();
    versionFont.setPointSize(12);
    m_versionLabel->setFont(versionFont);
    m_versionLabel->setStyleSheet("color: #cccccc; margin-bottom: 20px;");
    
    // Status label
    m_statusLabel = new QLabel(m_loadingMessages[0], this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    QFont statusFont = m_statusLabel->font();
    statusFont.setPointSize(11);
    m_statusLabel->setFont(statusFont);
    m_statusLabel->setStyleSheet("color: #aaaaaa; margin-bottom: 10px;");
    
    // Progress bar
    m_progressBar = new QProgressBar(this);
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    m_progressBar->setTextVisible(false);
    m_progressBar->setFixedHeight(8);
    m_progressBar->setStyleSheet(R"(
        QProgressBar {
            border: none;
            border-radius: 4px;
            background-color: #404040;
        }
        QProgressBar::chunk {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #2a82da, stop:1 #4a9eff);
            border-radius: 4px;
        }
    )");
    
    // Add widgets to layout
    m_mainLayout->addStretch();
    m_mainLayout->addWidget(m_logoLabel);
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_versionLabel);
    m_mainLayout->addStretch();
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addWidget(m_progressBar);
    m_mainLayout->addStretch();
    
    // Setup fade animation
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(m_opacityEffect);
    
    m_fadeAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeAnimation->setDuration(500);
    m_fadeAnimation->setStartValue(1.0);
    m_fadeAnimation->setEndValue(0.0);
    
    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, &SplashScreen::onAnimationFinished);
}

void SplashScreen::createLogo()
{
    // Create a simple logo using QPainter
    QPixmap logoPixmap(80, 80);
    logoPixmap.fill(Qt::transparent);
    
    QPainter painter(&logoPixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Create gradient for logo
    QRadialGradient gradient(40, 40, 35);
    gradient.setColorAt(0, QColor(42, 130, 218));
    gradient.setColorAt(0.7, QColor(26, 82, 138));
    gradient.setColorAt(1, QColor(15, 45, 75));
    
    painter.setBrush(QBrush(gradient));
    painter.setPen(QPen(QColor(255, 255, 255, 100), 2));
    painter.drawEllipse(5, 5, 70, 70);
    
    // Add "H" letter in the center
    painter.setPen(QPen(Qt::white, 3));
    QFont logoFont = painter.font();
    logoFont.setPointSize(32);
    logoFont.setBold(true);
    painter.setFont(logoFont);
    painter.drawText(logoPixmap.rect(), Qt::AlignCenter, "H");
    
    m_logoLabel = new QLabel(this);
    m_logoLabel->setPixmap(logoPixmap);
    m_logoLabel->setAlignment(Qt::AlignCenter);
}

void SplashScreen::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Create background gradient
    QLinearGradient gradient(0, 0, 0, height());
    gradient.setColorAt(0, QColor(45, 45, 45));
    gradient.setColorAt(0.5, QColor(35, 35, 35));
    gradient.setColorAt(1, QColor(25, 25, 25));
    
    // Draw rounded rectangle background
    painter.setBrush(QBrush(gradient));
    painter.setPen(QPen(QColor(80, 80, 80), 1));
    painter.drawRoundedRect(rect().adjusted(1, 1, -1, -1), 12, 12);
    
    // Add subtle border highlight
    painter.setPen(QPen(QColor(100, 100, 100, 50), 1));
    painter.drawRoundedRect(rect().adjusted(2, 2, -2, -2), 10, 10);
}

void SplashScreen::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    // Start with fade in effect
    m_opacityEffect->setOpacity(0.0);
    QPropertyAnimation *fadeIn = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    fadeIn->setDuration(300);
    fadeIn->setStartValue(0.0);
    fadeIn->setEndValue(1.0);
    fadeIn->start(QPropertyAnimation::DeleteWhenStopped);
}

void SplashScreen::startAnimation()
{
    m_progressTimer->start(60); // Update every 60ms for smooth animation
}

void SplashScreen::updateProgress()
{
    m_currentProgress += 1;
    
    if (m_currentProgress <= 100) {
        m_progressBar->setValue(m_currentProgress);
        
        // Update status message based on progress
        int messageProgress = (m_currentProgress * m_loadingMessages.size()) / 100;
        if (messageProgress < m_loadingMessages.size() && messageProgress != m_messageIndex) {
            m_messageIndex = messageProgress;
            m_statusLabel->setText(m_loadingMessages[m_messageIndex]);
        }
    }
    
    // Finish after minimum display time
    if (m_currentProgress >= 100) {
        m_progressTimer->stop();
        
        // Wait a bit more then start fade out
        QTimer::singleShot(500, [this]() {
            m_fadeAnimation->start();
        });
    }
}

void SplashScreen::onAnimationFinished()
{
    hide();
    emit finished();
    deleteLater();
}
