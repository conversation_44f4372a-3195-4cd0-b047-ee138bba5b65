#include "SplashScreen.h"
#include <QApplication>
#include <QShowEvent>
#include <QScreen>
#include <QPainter>
#include <QLinearGradient>
#include <QRadialGradient>
#include <QFontDatabase>

SplashScreen::SplashScreen(QWidget *parent)
    : QWidget(parent, Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint)
    , m_mainLayout(nullptr)
    , m_logoLayout(nullptr)
    , m_logo<PERSON>abel(nullptr)
    , m_titleLabel(nullptr)
    , m_versionLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_progressBar(nullptr)
    , m_progressTimer(new QTimer(this))
    , m_fadeAnimation(nullptr)
    , m_opacityEffect(nullptr)
    , m_currentProgress(0)
    , m_messageIndex(0)
{
    setFixedSize(SPLASH_WIDTH, SPLASH_HEIGHT);
    setAttribute(Qt::WA_TranslucentBackground);
    
    // Center on screen
    QScreen *screen = QApplication::primaryScreen();
    QRect screenGeometry = screen->geometry();
    int x = (screenGeometry.width() - SPLASH_WIDTH) / 2;
    int y = (screenGeometry.height() - SPLASH_HEIGHT) / 2;
    move(x, y);
    
    // Setup loading messages - hacker style
    m_loadingMessages = {
        "INITIALIZING HVNC CONTROLLER...",
        "LOADING NETWORK COMPONENTS...",
        "SETTING UP REMOTE DESKTOP INTERFACE...",
        "PREPARING APPLICATION LAUNCHERS...",
        "FINALIZING STARTUP..."
    };
    
    setupUI();
    
    // Setup progress timer
    connect(m_progressTimer, &QTimer::timeout, this, &SplashScreen::updateProgress);
}

SplashScreen::~SplashScreen() = default;

void SplashScreen::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(30, 30, 30, 30);
    m_mainLayout->setSpacing(20);
    
    // Logo section
    createLogo();
    
    // Title - hacker style
    m_titleLabel = new QLabel("HVNC CONTROLLER", this);
    m_titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont("Consolas", 24, QFont::Bold);
    m_titleLabel->setFont(titleFont);
    m_titleLabel->setStyleSheet(R"(
        color: #00ff00;
        margin: 10px 0;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        text-shadow: 0 0 10px #00ff00;
    )");

    // Version - hacker style
    m_versionLabel = new QLabel("VERSION 1.0.0", this);
    m_versionLabel->setAlignment(Qt::AlignCenter);
    QFont versionFont("Consolas", 12, QFont::Bold);
    m_versionLabel->setFont(versionFont);
    m_versionLabel->setStyleSheet(R"(
        color: #00cc00;
        margin-bottom: 20px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    )");

    // Status label - hacker style
    m_statusLabel = new QLabel(m_loadingMessages[0], this);
    m_statusLabel->setAlignment(Qt::AlignCenter);
    QFont statusFont("Consolas", 11, QFont::Bold);
    m_statusLabel->setFont(statusFont);
    m_statusLabel->setStyleSheet(R"(
        color: #00ff00;
        margin-bottom: 10px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    )");
    
    // Progress bar - hacker style
    m_progressBar = new QProgressBar(this);
    m_progressBar->setRange(0, 100);
    m_progressBar->setValue(0);
    m_progressBar->setTextVisible(true);
    m_progressBar->setFixedHeight(12);
    m_progressBar->setStyleSheet(R"(
        QProgressBar {
            border: 1px solid #00ff00;
            border-radius: 0px;
            background-color: #000000;
            color: #00ff00;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-weight: bold;
            text-align: center;
        }
        QProgressBar::chunk {
            background-color: #00ff00;
            border-radius: 0px;
        }
    )");
    
    // Add widgets to layout
    m_mainLayout->addStretch();
    m_mainLayout->addWidget(m_logoLabel);
    m_mainLayout->addWidget(m_titleLabel);
    m_mainLayout->addWidget(m_versionLabel);
    m_mainLayout->addStretch();
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addWidget(m_progressBar);
    m_mainLayout->addStretch();
    
    // Setup fade animation
    m_opacityEffect = new QGraphicsOpacityEffect(this);
    setGraphicsEffect(m_opacityEffect);
    
    m_fadeAnimation = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    m_fadeAnimation->setDuration(500);
    m_fadeAnimation->setStartValue(1.0);
    m_fadeAnimation->setEndValue(0.0);
    
    connect(m_fadeAnimation, &QPropertyAnimation::finished, this, &SplashScreen::onAnimationFinished);
}

void SplashScreen::createLogo()
{
    // Create hacker-style logo
    QPixmap logoPixmap(80, 80);
    logoPixmap.fill(Qt::transparent);

    QPainter painter(&logoPixmap);
    painter.setRenderHint(QPainter::Antialiasing);

    // Draw matrix-style grid background
    painter.setPen(QPen(QColor(0, 255, 0, 50), 1));
    for (int i = 0; i < 80; i += 10) {
        painter.drawLine(i, 0, i, 80);
        painter.drawLine(0, i, 80, i);
    }

    // Draw main logo border
    painter.setPen(QPen(QColor(0, 255, 0), 2));
    painter.setBrush(Qt::NoBrush);
    painter.drawRect(5, 5, 70, 70);

    // Add "H" letter in the center with hacker font
    painter.setPen(QPen(QColor(0, 255, 0), 3));
    QFont logoFont("Consolas", 32, QFont::Bold);
    painter.setFont(logoFont);
    painter.drawText(logoPixmap.rect(), Qt::AlignCenter, "H");

    // Add corner brackets
    painter.setPen(QPen(QColor(0, 255, 0), 2));
    painter.drawLine(5, 5, 15, 5);
    painter.drawLine(5, 5, 5, 15);
    painter.drawLine(65, 5, 75, 5);
    painter.drawLine(75, 5, 75, 15);
    painter.drawLine(5, 65, 15, 75);
    painter.drawLine(5, 65, 5, 75);
    painter.drawLine(65, 75, 75, 75);
    painter.drawLine(75, 65, 75, 75);

    m_logoLabel = new QLabel(this);
    m_logoLabel->setPixmap(logoPixmap);
    m_logoLabel->setAlignment(Qt::AlignCenter);
}

void SplashScreen::paintEvent(QPaintEvent *event)
{
    Q_UNUSED(event)

    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);

    // Hacker-style black background
    painter.fillRect(rect(), QColor(0, 0, 0));

    // Draw sharp green border
    painter.setPen(QPen(QColor(0, 255, 0), 2));
    painter.drawRect(rect().adjusted(1, 1, -1, -1));

    // Add inner green glow border
    painter.setPen(QPen(QColor(0, 255, 0, 100), 1));
    painter.drawRect(rect().adjusted(3, 3, -3, -3));

    // Add matrix-style corner brackets
    painter.setPen(QPen(QColor(0, 255, 0), 3));
    int cornerSize = 20;

    // Top-left corner
    painter.drawLine(5, 5, 5 + cornerSize, 5);
    painter.drawLine(5, 5, 5, 5 + cornerSize);

    // Top-right corner
    painter.drawLine(width() - 5 - cornerSize, 5, width() - 5, 5);
    painter.drawLine(width() - 5, 5, width() - 5, 5 + cornerSize);

    // Bottom-left corner
    painter.drawLine(5, height() - 5, 5 + cornerSize, height() - 5);
    painter.drawLine(5, height() - 5 - cornerSize, 5, height() - 5);

    // Bottom-right corner
    painter.drawLine(width() - 5 - cornerSize, height() - 5, width() - 5, height() - 5);
    painter.drawLine(width() - 5, height() - 5 - cornerSize, width() - 5, height() - 5);
}

void SplashScreen::showEvent(QShowEvent *event)
{
    QWidget::showEvent(event);
    
    // Start with fade in effect
    m_opacityEffect->setOpacity(0.0);
    QPropertyAnimation *fadeIn = new QPropertyAnimation(m_opacityEffect, "opacity", this);
    fadeIn->setDuration(300);
    fadeIn->setStartValue(0.0);
    fadeIn->setEndValue(1.0);
    fadeIn->start(QPropertyAnimation::DeleteWhenStopped);
}

void SplashScreen::startAnimation()
{
    m_progressTimer->start(60); // Update every 60ms for smooth animation
}

void SplashScreen::updateProgress()
{
    m_currentProgress += 1;
    
    if (m_currentProgress <= 100) {
        m_progressBar->setValue(m_currentProgress);
        
        // Update status message based on progress
        int messageProgress = (m_currentProgress * m_loadingMessages.size()) / 100;
        if (messageProgress < m_loadingMessages.size() && messageProgress != m_messageIndex) {
            m_messageIndex = messageProgress;
            m_statusLabel->setText(m_loadingMessages[m_messageIndex]);
        }
    }
    
    // Finish after minimum display time
    if (m_currentProgress >= 100) {
        m_progressTimer->stop();
        
        // Wait a bit more then start fade out
        QTimer::singleShot(500, [this]() {
            m_fadeAnimation->start();
        });
    }
}

void SplashScreen::onAnimationFinished()
{
    hide();
    emit finished();
    deleteLater();
}
