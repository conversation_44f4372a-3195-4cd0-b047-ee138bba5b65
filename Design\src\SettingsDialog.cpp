#include "SettingsDialog.h"
#include <QSettings>
#include <QTimer>
#include <QHeaderView>

SettingsDialog::SettingsDialog(QWidget *parent)
    : QDialog(parent)
    , m_tabWidget(nullptr)
    , m_imageQualityTab(nullptr)
    , m_compressionGroup(nullptr)
    , m_compressionSlider(nullptr)
    , m_compressionValueLabel(nullptr)
    , m_resolutionGroup(nullptr)
    , m_resolutionCombo(nullptr)
    , m_colorDepthGroup(nullptr)
    , m_colorDepthCombo(nullptr)
    , m_performanceTab(nullptr)
    , m_metricsGroup(nullptr)
    , m_fpsLabel(nullptr)
    , m_fpsBar(nullptr)
    , m_latencyLabel(nullptr)
    , m_latencyBar(nullptr)
    , m_cpuLabel(nullptr)
    , m_cpuBar(nullptr)
    , m_memoryLabel(nullptr)
    , m_memoryBar(nullptr)
    , m_logDisplay(nullptr)
    , m_connectionTab(nullptr)
    , m_serverGroup(nullptr)
    , m_serverIPEdit(nullptr)
    , m_serverPortSpin(nullptr)
    , m_optionsGroup(nullptr)
    , m_timeoutSpin(nullptr)
    , m_autoReconnectCheck(nullptr)
    , m_buttonLayout(nullptr)
    , m_applyButton(nullptr)
    , m_resetButton(nullptr)
    , m_okButton(nullptr)
    , m_cancelButton(nullptr)
    , m_compressionLevel(DEFAULT_COMPRESSION)
    , m_resolutionScale(DEFAULT_RESOLUTION_SCALE)
    , m_colorDepth(DEFAULT_COLOR_DEPTH)
    , m_serverIP("127.0.0.1")
    , m_serverPort(DEFAULT_PORT)
    , m_connectionTimeout(DEFAULT_TIMEOUT)
    , m_autoReconnect(true)
{
    setWindowTitle("HVNC Controller Settings");
    setModal(true);
    setFixedSize(600, 500);
    
    setupUI();
    loadSettings();
    
    // Setup performance update timer
    QTimer *performanceTimer = new QTimer(this);
    connect(performanceTimer, &QTimer::timeout, this, &SettingsDialog::updatePerformanceDisplay);
    performanceTimer->start(1000); // Update every second
}

SettingsDialog::~SettingsDialog() = default;

void SettingsDialog::setupUI()
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(16, 16, 16, 16);
    mainLayout->setSpacing(16);
    
    m_tabWidget = new QTabWidget(this);
    m_tabWidget->setStyleSheet(R"(
        QTabWidget::pane {
            border: 1px solid #555555;
            border-radius: 4px;
            background-color: #353535;
        }
        QTabBar::tab {
            background-color: #404040;
            border: 1px solid #555555;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #353535;
            border-bottom: 1px solid #353535;
        }
        QTabBar::tab:hover {
            background-color: #4a4a4a;
        }
    )");
    
    createImageQualityTab();
    createPerformanceTab();
    createConnectionTab();
    
    mainLayout->addWidget(m_tabWidget);
    
    // Dialog buttons
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->addStretch();
    
    m_applyButton = new QPushButton("Apply", this);
    m_resetButton = new QPushButton("Reset", this);
    m_okButton = new QPushButton("OK", this);
    m_cancelButton = new QPushButton("Cancel", this);
    
    m_buttonLayout->addWidget(m_applyButton);
    m_buttonLayout->addWidget(m_resetButton);
    m_buttonLayout->addWidget(m_okButton);
    m_buttonLayout->addWidget(m_cancelButton);
    
    mainLayout->addLayout(m_buttonLayout);
    
    // Connect button signals
    connect(m_applyButton, &QPushButton::clicked, this, &SettingsDialog::applySettings);
    connect(m_resetButton, &QPushButton::clicked, this, &SettingsDialog::resetSettings);
    connect(m_okButton, &QPushButton::clicked, [this]() {
        applySettings();
        accept();
    });
    connect(m_cancelButton, &QPushButton::clicked, this, &QDialog::reject);
}

void SettingsDialog::createImageQualityTab()
{
    m_imageQualityTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_imageQualityTab);
    layout->setContentsMargins(16, 16, 16, 16);
    layout->setSpacing(16);
    
    // Compression group
    m_compressionGroup = new QGroupBox("Image Compression", m_imageQualityTab);
    QVBoxLayout *compressionLayout = new QVBoxLayout(m_compressionGroup);
    
    QHBoxLayout *compressionSliderLayout = new QHBoxLayout();
    compressionSliderLayout->addWidget(new QLabel("Quality:"));
    
    m_compressionSlider = new QSlider(Qt::Horizontal);
    m_compressionSlider->setRange(1, 100);
    m_compressionSlider->setValue(m_compressionLevel);
    compressionSliderLayout->addWidget(m_compressionSlider);
    
    m_compressionValueLabel = new QLabel(QString("%1%").arg(m_compressionLevel));
    m_compressionValueLabel->setMinimumWidth(40);
    compressionSliderLayout->addWidget(m_compressionValueLabel);
    
    compressionLayout->addLayout(compressionSliderLayout);
    compressionLayout->addWidget(new QLabel("Higher values = better quality, lower compression"));
    
    // Resolution group
    m_resolutionGroup = new QGroupBox("Resolution Scaling", m_imageQualityTab);
    QVBoxLayout *resolutionLayout = new QVBoxLayout(m_resolutionGroup);
    
    QHBoxLayout *resolutionComboLayout = new QHBoxLayout();
    resolutionComboLayout->addWidget(new QLabel("Scale:"));
    
    m_resolutionCombo = new QComboBox();
    m_resolutionCombo->addItems({"25%", "50%", "75%", "100%", "125%", "150%"});
    m_resolutionCombo->setCurrentText("100%");
    resolutionComboLayout->addWidget(m_resolutionCombo);
    resolutionComboLayout->addStretch();
    
    resolutionLayout->addLayout(resolutionComboLayout);
    resolutionLayout->addWidget(new QLabel("Lower scaling reduces bandwidth usage"));
    
    // Color depth group
    m_colorDepthGroup = new QGroupBox("Color Depth", m_imageQualityTab);
    QVBoxLayout *colorDepthLayout = new QVBoxLayout(m_colorDepthGroup);
    
    QHBoxLayout *colorDepthComboLayout = new QHBoxLayout();
    colorDepthComboLayout->addWidget(new QLabel("Depth:"));
    
    m_colorDepthCombo = new QComboBox();
    m_colorDepthCombo->addItems({"16-bit (High Color)", "24-bit (True Color)", "32-bit (True Color + Alpha)"});
    m_colorDepthCombo->setCurrentIndex(2); // 32-bit default
    colorDepthComboLayout->addWidget(m_colorDepthCombo);
    colorDepthComboLayout->addStretch();
    
    colorDepthLayout->addLayout(colorDepthComboLayout);
    colorDepthLayout->addWidget(new QLabel("Higher color depth = better quality, more bandwidth"));
    
    layout->addWidget(m_compressionGroup);
    layout->addWidget(m_resolutionGroup);
    layout->addWidget(m_colorDepthGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(m_imageQualityTab, "Image Quality");
    
    // Connect signals
    connect(m_compressionSlider, &QSlider::valueChanged, this, &SettingsDialog::onCompressionChanged);
    connect(m_resolutionCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SettingsDialog::onResolutionChanged);
    connect(m_colorDepthCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SettingsDialog::onColorDepthChanged);
}

void SettingsDialog::createPerformanceTab()
{
    m_performanceTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_performanceTab);
    layout->setContentsMargins(16, 16, 16, 16);
    layout->setSpacing(16);
    
    // Performance metrics group
    m_metricsGroup = new QGroupBox("Real-time Performance", m_performanceTab);
    QGridLayout *metricsLayout = new QGridLayout(m_metricsGroup);
    metricsLayout->setSpacing(12);
    
    // FPS
    m_fpsLabel = new QLabel("FPS: 0");
    m_fpsBar = new QProgressBar();
    m_fpsBar->setRange(0, 60);
    m_fpsBar->setValue(0);
    metricsLayout->addWidget(m_fpsLabel, 0, 0);
    metricsLayout->addWidget(m_fpsBar, 0, 1);
    
    // Latency
    m_latencyLabel = new QLabel("Latency: 0ms");
    m_latencyBar = new QProgressBar();
    m_latencyBar->setRange(0, 500);
    m_latencyBar->setValue(0);
    metricsLayout->addWidget(m_latencyLabel, 1, 0);
    metricsLayout->addWidget(m_latencyBar, 1, 1);
    
    // CPU Usage
    m_cpuLabel = new QLabel("CPU: 0%");
    m_cpuBar = new QProgressBar();
    m_cpuBar->setRange(0, 100);
    m_cpuBar->setValue(0);
    metricsLayout->addWidget(m_cpuLabel, 2, 0);
    metricsLayout->addWidget(m_cpuBar, 2, 1);
    
    // Memory Usage
    m_memoryLabel = new QLabel("Memory: 0%");
    m_memoryBar = new QProgressBar();
    m_memoryBar->setRange(0, 100);
    m_memoryBar->setValue(0);
    metricsLayout->addWidget(m_memoryLabel, 3, 0);
    metricsLayout->addWidget(m_memoryBar, 3, 1);
    
    // Connection log
    QGroupBox *logGroup = new QGroupBox("Connection Log", m_performanceTab);
    QVBoxLayout *logLayout = new QVBoxLayout(logGroup);
    
    m_logDisplay = new QTextEdit();
    m_logDisplay->setReadOnly(true);
    m_logDisplay->setMaximumHeight(150);
    m_logDisplay->setStyleSheet(R"(
        QTextEdit {
            background-color: #1a1a1a;
            border: 1px solid #555555;
            border-radius: 4px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 10px;
        }
    )");
    
    // Add some sample log entries
    m_logDisplay->append("[14:30:15] Connection established to *************:4444");
    m_logDisplay->append("[14:30:16] Desktop capture started (1920x1080)");
    m_logDisplay->append("[14:30:17] Chrome launched successfully (PID: 1234)");
    
    logLayout->addWidget(m_logDisplay);
    
    layout->addWidget(m_metricsGroup);
    layout->addWidget(logGroup);
    layout->addStretch();
    
    m_tabWidget->addTab(m_performanceTab, "Performance");
}

void SettingsDialog::createConnectionTab()
{
    m_connectionTab = new QWidget();
    QVBoxLayout *layout = new QVBoxLayout(m_connectionTab);
    layout->setContentsMargins(16, 16, 16, 16);
    layout->setSpacing(16);

    // Server settings group
    m_serverGroup = new QGroupBox("Server Settings", m_connectionTab);
    QGridLayout *serverLayout = new QGridLayout(m_serverGroup);
    serverLayout->setSpacing(12);

    serverLayout->addWidget(new QLabel("IP Address:"), 0, 0);
    m_serverIPEdit = new QLineEdit(m_serverIP);
    m_serverIPEdit->setPlaceholderText("*************");
    serverLayout->addWidget(m_serverIPEdit, 0, 1);

    serverLayout->addWidget(new QLabel("Port:"), 1, 0);
    m_serverPortSpin = new QSpinBox();
    m_serverPortSpin->setRange(1, 65535);
    m_serverPortSpin->setValue(m_serverPort);
    serverLayout->addWidget(m_serverPortSpin, 1, 1);

    // Connection options group
    m_optionsGroup = new QGroupBox("Connection Options", m_connectionTab);
    QGridLayout *optionsLayout = new QGridLayout(m_optionsGroup);
    optionsLayout->setSpacing(12);

    optionsLayout->addWidget(new QLabel("Timeout (ms):"), 0, 0);
    m_timeoutSpin = new QSpinBox();
    m_timeoutSpin->setRange(1000, 30000);
    m_timeoutSpin->setValue(m_connectionTimeout);
    m_timeoutSpin->setSuffix(" ms");
    optionsLayout->addWidget(m_timeoutSpin, 0, 1);

    m_autoReconnectCheck = new QCheckBox("Auto-reconnect on disconnect");
    m_autoReconnectCheck->setChecked(m_autoReconnect);
    optionsLayout->addWidget(m_autoReconnectCheck, 1, 0, 1, 2);

    layout->addWidget(m_serverGroup);
    layout->addWidget(m_optionsGroup);
    layout->addStretch();

    m_tabWidget->addTab(m_connectionTab, "Connection");

    // Connect signals
    connect(m_serverIPEdit, &QLineEdit::textChanged, this, &SettingsDialog::onServerIPChanged);
    connect(m_serverPortSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SettingsDialog::onServerPortChanged);
    connect(m_timeoutSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SettingsDialog::onTimeoutChanged);
    connect(m_autoReconnectCheck, &QCheckBox::toggled, this, &SettingsDialog::onAutoReconnectToggled);
}

// Getters
int SettingsDialog::getCompressionLevel() const { return m_compressionLevel; }
float SettingsDialog::getResolutionScale() const { return m_resolutionScale; }
int SettingsDialog::getColorDepth() const { return m_colorDepth; }
QString SettingsDialog::getServerIP() const { return m_serverIP; }
int SettingsDialog::getServerPort() const { return m_serverPort; }
int SettingsDialog::getConnectionTimeout() const { return m_connectionTimeout; }
bool SettingsDialog::getAutoReconnect() const { return m_autoReconnect; }

// Slots
void SettingsDialog::onCompressionChanged(int value)
{
    m_compressionLevel = value;
    m_compressionValueLabel->setText(QString("%1%").arg(value));
}

void SettingsDialog::onResolutionChanged(int index)
{
    QStringList scales = {"0.25", "0.5", "0.75", "1.0", "1.25", "1.5"};
    if (index >= 0 && index < scales.size()) {
        m_resolutionScale = scales[index].toFloat();
    }
}

void SettingsDialog::onColorDepthChanged(int index)
{
    QList<int> depths = {16, 24, 32};
    if (index >= 0 && index < depths.size()) {
        m_colorDepth = depths[index];
    }
}

void SettingsDialog::onServerIPChanged()
{
    m_serverIP = m_serverIPEdit->text();
}

void SettingsDialog::onServerPortChanged(int port)
{
    m_serverPort = port;
}

void SettingsDialog::onTimeoutChanged(int timeout)
{
    m_connectionTimeout = timeout;
}

void SettingsDialog::onAutoReconnectToggled(bool enabled)
{
    m_autoReconnect = enabled;
}

void SettingsDialog::applySettings()
{
    saveSettings();
    emit settingsChanged();
}

void SettingsDialog::resetSettings()
{
    m_compressionLevel = DEFAULT_COMPRESSION;
    m_resolutionScale = DEFAULT_RESOLUTION_SCALE;
    m_colorDepth = DEFAULT_COLOR_DEPTH;
    m_serverIP = "127.0.0.1";
    m_serverPort = DEFAULT_PORT;
    m_connectionTimeout = DEFAULT_TIMEOUT;
    m_autoReconnect = true;

    // Update UI
    m_compressionSlider->setValue(m_compressionLevel);
    m_resolutionCombo->setCurrentText("100%");
    m_colorDepthCombo->setCurrentIndex(2);
    m_serverIPEdit->setText(m_serverIP);
    m_serverPortSpin->setValue(m_serverPort);
    m_timeoutSpin->setValue(m_connectionTimeout);
    m_autoReconnectCheck->setChecked(m_autoReconnect);
}

void SettingsDialog::loadSettings()
{
    QSettings settings;
    m_compressionLevel = settings.value("compression", DEFAULT_COMPRESSION).toInt();
    m_resolutionScale = settings.value("resolution_scale", DEFAULT_RESOLUTION_SCALE).toFloat();
    m_colorDepth = settings.value("color_depth", DEFAULT_COLOR_DEPTH).toInt();
    m_serverIP = settings.value("server_ip", "127.0.0.1").toString();
    m_serverPort = settings.value("server_port", DEFAULT_PORT).toInt();
    m_connectionTimeout = settings.value("timeout", DEFAULT_TIMEOUT).toInt();
    m_autoReconnect = settings.value("auto_reconnect", true).toBool();
}

void SettingsDialog::saveSettings()
{
    QSettings settings;
    settings.setValue("compression", m_compressionLevel);
    settings.setValue("resolution_scale", m_resolutionScale);
    settings.setValue("color_depth", m_colorDepth);
    settings.setValue("server_ip", m_serverIP);
    settings.setValue("server_port", m_serverPort);
    settings.setValue("timeout", m_connectionTimeout);
    settings.setValue("auto_reconnect", m_autoReconnect);
}

void SettingsDialog::updatePerformanceDisplay()
{
    // Mock performance data - in real implementation, this would come from the HVNC client
    static int mockFPS = 30;
    static int mockLatency = 50;
    static float mockCPU = 25.5f;
    static float mockMemory = 45.2f;

    // Simulate some variation
    mockFPS += (rand() % 11) - 5; // ±5
    mockFPS = qBound(0, mockFPS, 60);

    mockLatency += (rand() % 21) - 10; // ±10
    mockLatency = qBound(0, mockLatency, 500);

    mockCPU += (rand() % 11 - 5) * 0.1f; // ±0.5
    mockCPU = qBound(0.0f, mockCPU, 100.0f);

    mockMemory += (rand() % 11 - 5) * 0.1f; // ±0.5
    mockMemory = qBound(0.0f, mockMemory, 100.0f);

    // Update displays
    m_fpsLabel->setText(QString("FPS: %1").arg(mockFPS));
    m_fpsBar->setValue(mockFPS);

    m_latencyLabel->setText(QString("Latency: %1ms").arg(mockLatency));
    m_latencyBar->setValue(mockLatency);

    m_cpuLabel->setText(QString("CPU: %1%").arg(mockCPU, 0, 'f', 1));
    m_cpuBar->setValue(static_cast<int>(mockCPU));

    m_memoryLabel->setText(QString("Memory: %1%").arg(mockMemory, 0, 'f', 1));
    m_memoryBar->setValue(static_cast<int>(mockMemory));
}
