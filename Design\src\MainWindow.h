#pragma once

#include <QMainWindow>
#include <QHBoxLayout>
#include <QVBoxLayout>
#include <QFrame>
#include <QSplitter>
#include <QStatusBar>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QLabel>
#include <QTimer>
#include <QSystemTrayIcon>
#include <QCloseEvent>
#include <QScrollArea>
#include <QGroupBox>
#include <QSlider>
#include <QComboBox>
#include <QLineEdit>

class ApplicationLauncher;
class RemoteDesktopWidget;
class SettingsDialog;
class HVNCClient;
class PerformanceMonitor;
class ConnectionManager;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    void onApplicationLaunched(const QString &appName, int pid);
    void onConnectionStateChanged(bool connected);
    void onPerformanceUpdate(int fps, int latency, float cpuUsage, float memoryUsage);
    void showSettings();
    void toggleAlwaysOnTop();
    void showAbout();
    void onTrayIconActivated(QSystemTrayIcon::ActivationReason reason);
    void updateStatusBar();

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupSystemTray();
    void createLeftSidebar();
    void createMainContent();
    void createConnectionSettings();
    void createImageQualitySettings();
    void createPerformanceSettings();
    void createApplicationLauncher();
    void connectSignals();
    
    // UI Components
    QWidget *m_centralWidget;
    QHBoxLayout *m_mainLayout;
    QSplitter *m_splitter;
    
    // Left sidebar with settings
    QFrame *m_sidebarFrame;
    QVBoxLayout *m_sidebarLayout;
    QScrollArea *m_sidebarScrollArea;
    QWidget *m_sidebarContent;

    // Settings sections in sidebar
    QGroupBox *m_connectionGroup;
    QGroupBox *m_imageQualityGroup;
    QGroupBox *m_performanceGroup;
    QGroupBox *m_applicationGroup;
    
    // Main content area
    QFrame *m_contentFrame;
    QVBoxLayout *m_contentLayout;
    RemoteDesktopWidget *m_remoteDesktop;
    
    // Menu and toolbar
    QMenu *m_fileMenu;
    QMenu *m_viewMenu;
    QMenu *m_helpMenu;
    QAction *m_settingsAction;
    QAction *m_exitAction;
    QAction *m_alwaysOnTopAction;
    QAction *m_aboutAction;
    
    // Status bar
    QLabel *m_connectionStatusLabel;
    QLabel *m_fpsLabel;
    QLabel *m_latencyLabel;
    QLabel *m_cpuLabel;
    QLabel *m_memoryLabel;
    
    // System tray
    QSystemTrayIcon *m_trayIcon;
    QMenu *m_trayMenu;
    
    // Core components
    HVNCClient *m_hvncClient;
    PerformanceMonitor *m_performanceMonitor;
    ConnectionManager *m_connectionManager;
    SettingsDialog *m_settingsDialog;
    
    // Timers
    QTimer *m_statusUpdateTimer;
    
    // State
    bool m_isConnected;
    bool m_alwaysOnTop;
    
    static constexpr int SIDEBAR_WIDTH = 280;
    static constexpr int STATUS_UPDATE_INTERVAL = 1000; // 1 second
};
