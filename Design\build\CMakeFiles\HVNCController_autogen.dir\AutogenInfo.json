{"AUTOGEN_COMMAND_LINE_LENGTH_MAX": 32000, "BUILD_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen", "CMAKE_BINARY_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build", "CMAKE_CURRENT_BINARY_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build", "CMAKE_CURRENT_SOURCE_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design", "CMAKE_EXECUTABLE": "C:/Program Files/CMake/bin/cmake.exe", "CMAKE_LIST_FILES": ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/CMakeLists.txt", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineSystem.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystem.cmake.in", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/3.31.7/CMakeSystem.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Initialize.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-Determine-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerId.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCompilerIdDetection.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/ADSP-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/ARMCC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/ARMClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/AppleClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Borland-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Clang-DetermineCompilerInternal.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Cray-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CrayClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Embarcadero-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Fujitsu-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GHS-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/HP-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IAR-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Intel-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/NVHPC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/NVIDIA-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/OrangeC-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/PGI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/PathScale-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/SCO-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/TI-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/TIClang-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Tasking-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/Watcom-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/XL-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CompilerId/VS-10.vcxproj.in", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindBinUtils.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXCompiler.cmake.in", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/3.31.7/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeGenericSystem.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/WindowsPaths.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXInformation.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/MSVC.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCCompiler.cmake.in", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/3.31.7/CMakeRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeRCInformation.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestRCCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerABI.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeDetermineLinkerId.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeParseImplicitIncludeInfo.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeParseImplicitLinkInfo.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeParseLibraryArchitecture.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeTestCompilerCommon.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeDetermineCompilerSupport.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/FeatureTesting.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXCompiler.cmake.in", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/3.31.7/CMakeCXXCompiler.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC-CXX.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Platform/Linker/Windows-MSVC.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CMakeInspectCXXLinker.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCXXCompiler.cmake.in", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Config.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Targets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6VersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtFeature.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtFeatureCommon.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckFlagCommonConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/Qt6Dependencies.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindThreads.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckLibraryExists.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckIncludeFileCXX.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/FindWrapAtomic.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6ZlibPrivate/Qt6ZlibPrivateVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6EntryPointPrivate/Qt6EntryPointPrivateVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Core/QtInstallPaths.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/GNUInstallDirs.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6WidgetsTools/Qt6WidgetsToolsVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6/FindWrapVulkanHeaders.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindVulkan.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/FindPackageMessage.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6GuiTools/Qt6GuiToolsVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6GuiPlugins.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QGifPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QGifPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QGifPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QICOPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QICOPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QICOPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QJpegPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QJpegPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QJpegPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QPdfPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QPdfPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QPdfPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QPdfPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QPdfPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgIconPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QSvgPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QTuioTouchPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Gui/Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsMacros.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6WidgetsPlugins.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6QWindowsVistaStylePluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6QWindowsVistaStylePluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6QWindowsVistaStylePluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6QWindowsVistaStylePluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Widgets/Qt6QWindowsVistaStylePluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersion.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkConfigVersionImpl.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkDependencies.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkVersionlessTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6NetworkPlugins.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QNLMNIPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QSchannelBackendPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendCertOnlyPluginAdditionalTargetInfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginConfig.cmake", "C:/Program Files/CMake/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-debug.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginTargets-relwithdebinfo.cmake", "C:/Qt/6.5.3/msvc2019_64/lib/cmake/Qt6Network/Qt6QTlsBackendOpenSSLPluginAdditionalTargetInfo.cmake", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/resources/resources.qrc"], "CMAKE_SOURCE_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/ApplicationLauncher.h", "MU", "UVLADIE3JM/moc_ApplicationLauncher.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/ConnectionManager.h", "MU", "UVLADIE3JM/moc_ConnectionManager.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/EmbeddedHVNCServer.h", "MU", "UVLADIE3JM/moc_EmbeddedHVNCServer.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/HVNCClient.h", "MU", "UVLADIE3JM/moc_HVNCClient.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/MainWindow.h", "MU", "UVLADIE3JM/moc_MainWindow.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/PerformanceMonitor.h", "MU", "UVLADIE3JM/moc_PerformanceMonitor.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/RemoteDesktopWidget.h", "MU", "UVLADIE3JM/moc_RemoteDesktopWidget.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/SettingsDialog.h", "MU", "UVLADIE3JM/moc_SettingsDialog.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/SplashScreen.h", "MU", "UVLADIE3JM/moc_SplashScreen.cpp", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/common/SimpleLogger.h", "MU", "FYJGO5BPVD/moc_SimpleLogger.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include", "INCLUDE_DIR_Debug": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_Debug", "INCLUDE_DIR_MinSizeRel": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_MinSizeRel", "INCLUDE_DIR_RelWithDebInfo": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_RelWithDebInfo", "INCLUDE_DIR_Release": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/include_Release", "MOC_COMPILATION_FILE": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/mocs_compilation.cpp", "MOC_COMPILATION_FILE_Debug": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/mocs_compilation_Debug.cpp", "MOC_COMPILATION_FILE_MinSizeRel": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/mocs_compilation_MinSizeRel.cpp", "MOC_COMPILATION_FILE_RelWithDebInfo": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/mocs_compilation_RelWithDebInfo.cpp", "MOC_COMPILATION_FILE_Release": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/HVNCController_autogen/mocs_compilation_Release.cpp", "MOC_DEFINITIONS": [], "MOC_DEFINITIONS_Debug": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_MinSizeRel": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_RelWithDebInfo": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEFINITIONS_Release": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NETWORK_LIB", "QT_NO_DEBUG", "QT_WIDGETS_LIB", "UNICODE", "WIN32", "WIN64", "_ENABLE_EXTENDED_ALIGNED_STORAGE", "_UNICODE", "_WIN64"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": [], "MOC_INCLUDES_Debug": ["C:/Users/<USER>/OneDrive/Desktop/HVNC/common", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Client", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Server", "C:/Qt/6.5.3/msvc2019_64/include/QtCore", "C:/Qt/6.5.3/msvc2019_64/include", "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc", "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets", "C:/Qt/6.5.3/msvc2019_64/include/QtGui", "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork"], "MOC_INCLUDES_MinSizeRel": ["C:/Users/<USER>/OneDrive/Desktop/HVNC/common", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Client", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Server", "C:/Qt/6.5.3/msvc2019_64/include/QtCore", "C:/Qt/6.5.3/msvc2019_64/include", "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc", "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets", "C:/Qt/6.5.3/msvc2019_64/include/QtGui", "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork"], "MOC_INCLUDES_RelWithDebInfo": ["C:/Users/<USER>/OneDrive/Desktop/HVNC/common", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Client", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Server", "C:/Qt/6.5.3/msvc2019_64/include/QtCore", "C:/Qt/6.5.3/msvc2019_64/include", "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc", "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets", "C:/Qt/6.5.3/msvc2019_64/include/QtGui", "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork"], "MOC_INCLUDES_Release": ["C:/Users/<USER>/OneDrive/Desktop/HVNC/common", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Client", "C:/Users/<USER>/OneDrive/Desktop/HVNC/Server", "C:/Qt/6.5.3/msvc2019_64/include/QtCore", "C:/Qt/6.5.3/msvc2019_64/include", "C:/Qt/6.5.3/msvc2019_64/mkspecs/win32-msvc", "C:/Qt/6.5.3/msvc2019_64/include/QtWidgets", "C:/Qt/6.5.3/msvc2019_64/include/QtGui", "C:/Qt/6.5.3/msvc2019_64/include/QtNetwork"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": [], "MOC_PREDEFS_FILE": "", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": true, "PARALLEL": 10, "PARSE_CACHE_FILE": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/ParseCache.txt", "PARSE_CACHE_FILE_Debug": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/ParseCache_Debug.txt", "PARSE_CACHE_FILE_MinSizeRel": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/ParseCache_MinSizeRel.txt", "PARSE_CACHE_FILE_RelWithDebInfo": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/ParseCache_RelWithDebInfo.txt", "PARSE_CACHE_FILE_Release": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/ParseCache_Release.txt", "QT_MOC_EXECUTABLE": "C:/Qt/6.5.3/msvc2019_64/./bin/moc.exe", "QT_UIC_EXECUTABLE": "C:/Qt/6.5.3/msvc2019_64/./bin/uic.exe", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 5, "SETTINGS_FILE": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenUsed.txt", "SETTINGS_FILE_Debug": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenUsed_Debug.txt", "SETTINGS_FILE_MinSizeRel": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenUsed_MinSizeRel.txt", "SETTINGS_FILE_RelWithDebInfo": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenUsed_RelWithDebInfo.txt", "SETTINGS_FILE_Release": "C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/build/CMakeFiles/HVNCController_autogen.dir/AutogenUsed_Release.txt", "SOURCES": [["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/ApplicationLauncher.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/ConnectionManager.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/EmbeddedHVNCServer.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/HVNCClient.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/MainWindow.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/PerformanceMonitor.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/RemoteDesktopWidget.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/SettingsDialog.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/SplashScreen.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/Design/src/main.cpp", "MU", null], ["C:/Users/<USER>/OneDrive/Desktop/HVNC/common/SimpleLogger.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}