#include "ConnectionManager.h"
#include "HVNCClient.h"
#include <QDebug>

ConnectionManager::ConnectionManager(QObject *parent)
    : QObject(parent)
    , m_client(nullptr)
    , m_reconnectTimer(new QTimer(this))
    , m_serverHost("127.0.0.1")
    , m_serverPort(DEFAULT_PORT)
    , m_connectionTimeout(DEFAULT_TIMEOUT)
    , m_autoReconnect(true)
    , m_isConnected(false)
    , m_isConnecting(false)
    , m_userDisconnected(false)
    , m_connectionAttempts(0)
    , m_maxReconnectAttempts(MAX_RECONNECT_ATTEMPTS)
    , m_reconnectDelay(INITIAL_RECONNECT_DELAY)
{
    // Setup reconnect timer
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &ConnectionManager::attemptReconnect);
    
    // Create HVNC client
    m_client = new HVNCClient(this);
    
    // Connect client signals
    connect(m_client, &HVNCClient::connected, this, &ConnectionManager::onClientConnected);
    connect(m_client, &HVNCClient::disconnected, this, &ConnectionManager::onClientDisconnected);
    connect(m_client, &HVNCClient::errorOccurred, this, &ConnectionManager::onClientError);
}

ConnectionManager::~ConnectionManager()
{
    disconnectFromServer();
}

void ConnectionManager::setConnectionSettings(const QString &host, int port, int timeout, bool autoReconnect)
{
    m_serverHost = host;
    m_serverPort = port;
    m_connectionTimeout = timeout;
    m_autoReconnect = autoReconnect;
    
    if (!m_autoReconnect) {
        stopReconnectTimer();
    }
}

void ConnectionManager::connectToServer()
{
    if (m_isConnected || m_isConnecting) {
        return;
    }
    
    m_isConnecting = true;
    m_userDisconnected = false;
    m_connectionAttempts++;
    
    qDebug() << "Attempting to connect to" << m_serverHost << ":" << m_serverPort 
             << "(attempt" << m_connectionAttempts << ")";
    
    if (!m_client->connectToServer(m_serverHost, m_serverPort)) {
        m_isConnecting = false;
        m_lastError = "Failed to connect to server";
        emit connectionError(m_lastError);
        
        if (m_autoReconnect && m_connectionAttempts < m_maxReconnectAttempts) {
            startReconnectTimer();
        }
    }
}

void ConnectionManager::disconnectFromServer()
{
    m_userDisconnected = true;
    stopReconnectTimer();
    
    if (m_client) {
        m_client->disconnectFromServer();
    }
    
    resetConnectionState();
}

bool ConnectionManager::isConnected() const
{
    return m_isConnected;
}

bool ConnectionManager::isAutoReconnectEnabled() const
{
    return m_autoReconnect;
}

QString ConnectionManager::getLastError() const
{
    return m_lastError;
}

int ConnectionManager::getConnectionAttempts() const
{
    return m_connectionAttempts;
}

void ConnectionManager::onClientConnected()
{
    m_isConnected = true;
    m_isConnecting = false;
    m_lastError.clear();
    
    // Reset reconnection parameters on successful connection
    m_connectionAttempts = 0;
    m_reconnectDelay = INITIAL_RECONNECT_DELAY;
    stopReconnectTimer();
    
    qDebug() << "Successfully connected to HVNC server";
    emit connectionStateChanged(true);
}

void ConnectionManager::onClientDisconnected()
{
    bool wasConnected = m_isConnected;
    m_isConnected = false;
    m_isConnecting = false;
    
    qDebug() << "Disconnected from HVNC server";
    
    if (wasConnected) {
        emit connectionStateChanged(false);
    }
    
    // Attempt reconnection if enabled and not user-initiated
    if (m_autoReconnect && !m_userDisconnected && m_connectionAttempts < m_maxReconnectAttempts) {
        startReconnectTimer();
    } else if (m_connectionAttempts >= m_maxReconnectAttempts) {
        m_lastError = QString("Maximum reconnection attempts (%1) reached").arg(m_maxReconnectAttempts);
        emit connectionError(m_lastError);
    }
}

void ConnectionManager::onClientError(const QString &error)
{
    m_lastError = error;
    m_isConnecting = false;
    
    qDebug() << "HVNC client error:" << error;
    emit connectionError(error);
    
    // Attempt reconnection if enabled
    if (m_autoReconnect && !m_userDisconnected && m_connectionAttempts < m_maxReconnectAttempts) {
        startReconnectTimer();
    }
}

void ConnectionManager::attemptReconnect()
{
    if (m_userDisconnected || m_isConnected || m_isConnecting) {
        return;
    }
    
    qDebug() << "Attempting reconnection in" << m_reconnectDelay << "ms (attempt" << (m_connectionAttempts + 1) << ")";
    emit reconnectAttempt(m_connectionAttempts + 1);
    
    connectToServer();
}

void ConnectionManager::resetConnectionState()
{
    m_isConnected = false;
    m_isConnecting = false;
    m_connectionAttempts = 0;
    m_reconnectDelay = INITIAL_RECONNECT_DELAY;
}

void ConnectionManager::startReconnectTimer()
{
    if (!m_reconnectTimer->isActive()) {
        m_reconnectTimer->start(m_reconnectDelay);
        
        // Exponential backoff with maximum delay
        m_reconnectDelay = qMin(m_reconnectDelay * 2, MAX_RECONNECT_DELAY);
    }
}

void ConnectionManager::stopReconnectTimer()
{
    if (m_reconnectTimer->isActive()) {
        m_reconnectTimer->stop();
    }
}
