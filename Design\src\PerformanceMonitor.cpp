#include "PerformanceMonitor.h"
#include <QDebug>

#ifdef _WIN32
#include <windows.h>
#include <psapi.h>
#endif

PerformanceMonitor::PerformanceMonitor(QObject *parent)
    : QObject(parent)
    , m_updateTimer(new QTimer(this))
    , m_fpsTimer(new QTimer(this))
    , m_elapsedTimer(new QElapsedTimer())
    , m_currentFPS(0)
    , m_frameCount(0)
    , m_currentLatency(0)
    , m_currentCPUUsage(0.0f)
    , m_currentMemoryUsage(0.0f)
#ifdef _WIN32
    , m_processHandle(nullptr)
    , m_systemHandle(nullptr)
    , m_lastCPUTime(0)
    , m_lastSystemTime(0)
#endif
{
    // Setup timers
    connect(m_updateTimer, &QTimer::timeout, this, &PerformanceMonitor::updatePerformanceMetrics);
    connect(m_fpsTimer, &QTimer::timeout, this, &PerformanceMonitor::calculateFPS);
    
    m_elapsedTimer->start();
    
#ifdef _WIN32
    // Initialize Windows performance monitoring
    m_processHandle = GetCurrentProcess();
    m_systemHandle = GetCurrentProcess(); // Will be used for system-wide metrics
#endif
}

PerformanceMonitor::~PerformanceMonitor()
{
    stopMonitoring();
    delete m_elapsedTimer;
}

void PerformanceMonitor::startMonitoring()
{
    m_updateTimer->start(UPDATE_INTERVAL);
    m_fpsTimer->start(FPS_CALCULATION_INTERVAL);
    
    // Reset counters
    m_frameTimestamps.clear();
    m_latencyHistory.clear();
    m_currentFPS = 0;
    m_frameCount = 0;
    m_currentLatency = 0;
}

void PerformanceMonitor::stopMonitoring()
{
    m_updateTimer->stop();
    m_fpsTimer->stop();
}

int PerformanceMonitor::getCurrentFPS() const
{
    return m_currentFPS;
}

int PerformanceMonitor::getCurrentLatency() const
{
    return m_currentLatency;
}

float PerformanceMonitor::getCurrentCPUUsage() const
{
    return m_currentCPUUsage;
}

float PerformanceMonitor::getCurrentMemoryUsage() const
{
    return m_currentMemoryUsage;
}

void PerformanceMonitor::recordFrameReceived()
{
    qint64 currentTime = m_elapsedTimer->elapsed();
    m_frameTimestamps.enqueue(currentTime);
    m_frameCount++;
    
    // Keep only recent frames
    while (m_frameTimestamps.size() > MAX_FRAME_HISTORY) {
        m_frameTimestamps.dequeue();
    }
}

void PerformanceMonitor::recordLatency(int latencyMs)
{
    m_latencyHistory.enqueue(latencyMs);
    
    // Keep only recent latency measurements
    while (m_latencyHistory.size() > MAX_LATENCY_HISTORY) {
        m_latencyHistory.dequeue();
    }
    
    // Calculate average latency
    if (!m_latencyHistory.isEmpty()) {
        int sum = 0;
        for (int latency : m_latencyHistory) {
            sum += latency;
        }
        m_currentLatency = sum / m_latencyHistory.size();
    }
}

void PerformanceMonitor::updatePerformanceMetrics()
{
    updateCPUUsage();
    updateMemoryUsage();
    
    emit performanceUpdate(m_currentFPS, m_currentLatency, m_currentCPUUsage, m_currentMemoryUsage);
}

void PerformanceMonitor::calculateFPS()
{
    qint64 currentTime = m_elapsedTimer->elapsed();
    qint64 oneSecondAgo = currentTime - 1000;
    
    // Count frames in the last second
    int framesInLastSecond = 0;
    for (qint64 timestamp : m_frameTimestamps) {
        if (timestamp >= oneSecondAgo) {
            framesInLastSecond++;
        }
    }
    
    m_currentFPS = framesInLastSecond;
}

void PerformanceMonitor::updateCPUUsage()
{
#ifdef _WIN32
    FILETIME idleTime, kernelTime, userTime;
    if (GetSystemTimes(&idleTime, &kernelTime, &userTime)) {
        quint64 idle = (static_cast<quint64>(idleTime.dwHighDateTime) << 32) | idleTime.dwLowDateTime;
        quint64 kernel = (static_cast<quint64>(kernelTime.dwHighDateTime) << 32) | kernelTime.dwLowDateTime;
        quint64 user = (static_cast<quint64>(userTime.dwHighDateTime) << 32) | userTime.dwLowDateTime;
        
        quint64 system = kernel + user;
        
        if (m_lastSystemTime != 0) {
            quint64 systemDelta = system - m_lastSystemTime;
            quint64 idleDelta = idle - m_lastCPUTime;
            
            if (systemDelta > 0) {
                m_currentCPUUsage = (1.0f - static_cast<float>(idleDelta) / systemDelta) * 100.0f;
                m_currentCPUUsage = qBound(0.0f, m_currentCPUUsage, 100.0f);
            }
        }
        
        m_lastSystemTime = system;
        m_lastCPUTime = idle;
    }
#else
    // Mock CPU usage for non-Windows platforms
    static float mockCPU = 25.0f;
    mockCPU += (rand() % 11 - 5) * 0.5f; // ±2.5%
    m_currentCPUUsage = qBound(0.0f, mockCPU, 100.0f);
#endif
}

void PerformanceMonitor::updateMemoryUsage()
{
#ifdef _WIN32
    PROCESS_MEMORY_COUNTERS_EX pmc;
    if (GetProcessMemoryInfo(m_processHandle, reinterpret_cast<PROCESS_MEMORY_COUNTERS*>(&pmc), sizeof(pmc))) {
        // Get system memory info
        MEMORYSTATUSEX memInfo;
        memInfo.dwLength = sizeof(MEMORYSTATUSEX);
        if (GlobalMemoryStatusEx(&memInfo)) {
            // Calculate memory usage percentage
            m_currentMemoryUsage = static_cast<float>(pmc.WorkingSetSize) / memInfo.ullTotalPhys * 100.0f;
            m_currentMemoryUsage = qBound(0.0f, m_currentMemoryUsage, 100.0f);
        }
    }
#else
    // Mock memory usage for non-Windows platforms
    static float mockMemory = 45.0f;
    mockMemory += (rand() % 11 - 5) * 0.3f; // ±1.5%
    m_currentMemoryUsage = qBound(0.0f, mockMemory, 100.0f);
#endif
}
