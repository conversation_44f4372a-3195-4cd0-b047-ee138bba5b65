#pragma once

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QLabel>
#include <QSlider>
#include <QComboBox>
#include <QLineEdit>
#include <QSpinBox>
#include <QCheckBox>
#include <QPushButton>
#include <QProgressBar>
#include <QTextEdit>
#include <QSplitter>

class SettingsDialog : public QDialog
{
    Q_OBJECT

public:
    explicit SettingsDialog(QWidget *parent = nullptr);
    ~SettingsDialog();

    // Getters for settings values
    int getCompressionLevel() const;
    float getResolutionScale() const;
    int getColorDepth() const;
    QString getServerIP() const;
    int getServerPort() const;
    int getConnectionTimeout() const;
    bool getAutoReconnect() const;

signals:
    void settingsChanged();

private slots:
    void onCompressionChanged(int value);
    void onResolutionChanged(int index);
    void onColorDepthChanged(int index);
    void onServerIPChanged();
    void onServerPortChanged(int port);
    void onTimeoutChanged(int timeout);
    void onAutoReconnectToggled(bool enabled);
    void applySettings();
    void resetSettings();
    void updatePerformanceDisplay();

private:
    void setupUI();
    void createImageQualityTab();
    void createPerformanceTab();
    void createConnectionTab();
    void loadSettings();
    void saveSettings();
    
    QTabWidget *m_tabWidget;
    
    // Image Quality Tab
    QWidget *m_imageQualityTab;
    QGroupBox *m_compressionGroup;
    QSlider *m_compressionSlider;
    QLabel *m_compressionValueLabel;
    QGroupBox *m_resolutionGroup;
    QComboBox *m_resolutionCombo;
    QGroupBox *m_colorDepthGroup;
    QComboBox *m_colorDepthCombo;
    
    // Performance Tab
    QWidget *m_performanceTab;
    QGroupBox *m_metricsGroup;
    QLabel *m_fpsLabel;
    QProgressBar *m_fpsBar;
    QLabel *m_latencyLabel;
    QProgressBar *m_latencyBar;
    QLabel *m_cpuLabel;
    QProgressBar *m_cpuBar;
    QLabel *m_memoryLabel;
    QProgressBar *m_memoryBar;
    QTextEdit *m_logDisplay;
    
    // Connection Tab
    QWidget *m_connectionTab;
    QGroupBox *m_serverGroup;
    QLineEdit *m_serverIPEdit;
    QSpinBox *m_serverPortSpin;
    QGroupBox *m_optionsGroup;
    QSpinBox *m_timeoutSpin;
    QCheckBox *m_autoReconnectCheck;
    
    // Dialog buttons
    QHBoxLayout *m_buttonLayout;
    QPushButton *m_applyButton;
    QPushButton *m_resetButton;
    QPushButton *m_okButton;
    QPushButton *m_cancelButton;
    
    // Current values
    int m_compressionLevel;
    float m_resolutionScale;
    int m_colorDepth;
    QString m_serverIP;
    int m_serverPort;
    int m_connectionTimeout;
    bool m_autoReconnect;
    
    static constexpr int DEFAULT_COMPRESSION = 85;
    static constexpr float DEFAULT_RESOLUTION_SCALE = 1.0f;
    static constexpr int DEFAULT_COLOR_DEPTH = 32;
    static constexpr int DEFAULT_PORT = 4444;
    static constexpr int DEFAULT_TIMEOUT = 5000;
};
