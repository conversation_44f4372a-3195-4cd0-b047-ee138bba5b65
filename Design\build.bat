@echo off
echo Building HVNC Controller Qt6 Application...

REM Check if Qt6 path is set
if "%CMAKE_PREFIX_PATH%"=="" (
    echo.
    echo WARNING: CMAKE_PREFIX_PATH not set!
    echo Please set Qt6 path first, for example:
    echo set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\msvc2019_64
    echo.
    echo Attempting to find Qt6 automatically...

    REM Try common Qt6 installation paths
    if exist "C:\Qt\6.5.3\msvc2019_64" set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\msvc2019_64
    if exist "C:\Qt\6.5.0\msvc2022_64" set CMAKE_PREFIX_PATH=C:\Qt\6.5.0\msvc2022_64
    if exist "C:\Qt\6.4.0\msvc2022_64" set CMAKE_PREFIX_PATH=C:\Qt\6.4.0\msvc2022_64
    if exist "C:\Qt\6.6.0\msvc2022_64" set CMAKE_PREFIX_PATH=C:\Qt\6.6.0\msvc2022_64
)

if not "%CMAKE_PREFIX_PATH%"=="" (
    echo Using Qt6 path: %CMAKE_PREFIX_PATH%
)

REM Create build directory
if exist build rmdir /s /q build
if not exist build mkdir build
cd build

REM Configure with CMake
echo Configuring project...
if not "%CMAKE_PREFIX_PATH%"=="" (
    cmake .. -G "Visual Studio 17 2022" -A x64 -DCMAKE_PREFIX_PATH=%CMAKE_PREFIX_PATH%
) else (
    cmake .. -G "Visual Studio 17 2022" -A x64
)

if %ERRORLEVEL% neq 0 (
    echo.
    echo CMake configuration failed!
    echo.
    echo Make sure Qt6 is installed and CMAKE_PREFIX_PATH is set correctly.
    echo Example: set CMAKE_PREFIX_PATH=C:\Qt\6.5.3\msvc2019_64
    echo.
    pause
    exit /b 1
)

REM Build the project
echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
echo Executable location: build\Release\HVNC Controller.exe
echo.
pause
