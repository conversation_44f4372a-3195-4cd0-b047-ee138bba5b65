#pragma once

#include <QObject>
#include <QTimer>
#include <QElapsedTimer>
#include <QQueue>

class PerformanceMonitor : public QObject
{
    Q_OBJECT

public:
    explicit PerformanceMonitor(QObject *parent = nullptr);
    ~PerformanceMonitor();

    void startMonitoring();
    void stopMonitoring();
    
    int getCurrentFPS() const;
    int getCurrentLatency() const;
    float getCurrentCPUUsage() const;
    float getCurrentMemoryUsage() const;
    
    void recordFrameReceived();
    void recordLatency(int latencyMs);

signals:
    void performanceUpdate(int fps, int latency, float cpuUsage, float memoryUsage);

private slots:
    void updatePerformanceMetrics();
    void calculateFPS();

private:
    void updateCPUUsage();
    void updateMemoryUsage();
    
    QTimer *m_updateTimer;
    QTimer *m_fpsTimer;
    QElapsedTimer *m_elapsedTimer;
    
    // FPS tracking
    QQueue<qint64> m_frameTimestamps;
    int m_currentFPS;
    int m_frameCount;
    
    // Latency tracking
    QQueue<int> m_latencyHistory;
    int m_currentLatency;
    
    // System metrics
    float m_currentCPUUsage;
    float m_currentMemoryUsage;
    
    // Windows-specific handles for performance monitoring
#ifdef _WIN32
    void* m_processHandle;
    void* m_systemHandle;
    quint64 m_lastCPUTime;
    quint64 m_lastSystemTime;
#endif
    
    static constexpr int UPDATE_INTERVAL = 1000; // 1 second
    static constexpr int FPS_CALCULATION_INTERVAL = 1000; // 1 second
    static constexpr int MAX_FRAME_HISTORY = 120; // 2 seconds at 60 FPS
    static constexpr int MAX_LATENCY_HISTORY = 10;
};
