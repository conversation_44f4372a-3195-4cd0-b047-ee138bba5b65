#include "ApplicationLauncher.h"
#include <QMessageBox>
#include <QPainter>
#include <QFont>

ApplicationLauncher::ApplicationLauncher(QWidget *parent)
    : QWidget(parent)
    , m_mainLayout(nullptr)
    , m_groupBox(nullptr)
    , m_gridLayout(nullptr)
    , m_chromeButton(nullptr)
    , m_firefoxButton(nullptr)
    , m_edgeButton(nullptr)
    , m_brave<PERSON>utton(nullptr)
    , m_powershellButton(nullptr)
    , m_explorerButton(nullptr)
{
    setupUI();
}

ApplicationLauncher::~ApplicationLauncher() = default;

void ApplicationLauncher::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    m_groupBox = new QGroupBox("Applications", this);
    m_groupBox->setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            font-size: 14px;
            color: white;
            border: 2px solid #555555;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 12px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
        }
    )");
    
    m_gridLayout = new QGridLayout(m_groupBox);
    m_gridLayout->setContentsMargins(12, 20, 12, 12);
    m_gridLayout->setSpacing(8);
    
    // Create application buttons in a 2-column grid
    createApplicationButton("Chrome", "chrome", SLOT(launchChrome()), m_gridLayout, 0, 0);
    createApplicationButton("Firefox", "firefox", SLOT(launchFirefox()), m_gridLayout, 0, 1);
    createApplicationButton("Edge", "edge", SLOT(launchEdge()), m_gridLayout, 1, 0);
    createApplicationButton("Brave", "brave", SLOT(launchBrave()), m_gridLayout, 1, 1);
    createApplicationButton("PowerShell", "powershell", SLOT(launchPowerShell()), m_gridLayout, 2, 0);
    createApplicationButton("Explorer", "explorer", SLOT(launchFileExplorer()), m_gridLayout, 2, 1);
    
    m_mainLayout->addWidget(m_groupBox);
}

void ApplicationLauncher::createApplicationButton(const QString &text, const QString &iconName,
                                                const char *slot, QGridLayout *layout, int row, int col)
{
    QPushButton *button = new QPushButton(text, this);
    button->setFixedHeight(BUTTON_HEIGHT);
    button->setIcon(createApplicationIcon(iconName, QColor(42, 130, 218)));
    button->setIconSize(QSize(ICON_SIZE, ICON_SIZE));
    
    button->setStyleSheet(R"(
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            border-radius: 6px;
            padding: 8px 12px;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: left;
        }
        QPushButton:hover {
            background-color: #4a4a4a;
            border-color: #666666;
        }
        QPushButton:pressed {
            background-color: #363636;
        }
    )");
    
    connect(button, SIGNAL(clicked()), this, slot);
    layout->addWidget(button, row, col);
    
    // Store button references for potential future use
    if (text == "Chrome") m_chromeButton = button;
    else if (text == "Firefox") m_firefoxButton = button;
    else if (text == "Edge") m_edgeButton = button;
    else if (text == "Brave") m_braveButton = button;
    else if (text == "PowerShell") m_powershellButton = button;
    else if (text == "Explorer") m_explorerButton = button;
}

QIcon ApplicationLauncher::createApplicationIcon(const QString &appName, const QColor &color)
{
    QPixmap pixmap(ICON_SIZE, ICON_SIZE);
    pixmap.fill(Qt::transparent);
    
    QPainter painter(&pixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Create different icons based on app name
    if (appName == "chrome") {
        // Chrome-like circular icon
        painter.setBrush(QBrush(QColor(66, 133, 244)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawEllipse(2, 2, ICON_SIZE-4, ICON_SIZE-4);
        
        painter.setBrush(QBrush(Qt::white));
        painter.drawEllipse(6, 6, ICON_SIZE-12, ICON_SIZE-12);
        
        painter.setBrush(QBrush(QColor(66, 133, 244)));
        painter.drawEllipse(8, 8, ICON_SIZE-16, ICON_SIZE-16);
    }
    else if (appName == "firefox") {
        // Firefox-like icon
        painter.setBrush(QBrush(QColor(255, 149, 0)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawEllipse(2, 2, ICON_SIZE-4, ICON_SIZE-4);
        
        painter.setPen(QPen(Qt::white, 2));
        painter.drawArc(6, 6, ICON_SIZE-12, ICON_SIZE-12, 45*16, 270*16);
    }
    else if (appName == "edge") {
        // Edge-like icon
        painter.setBrush(QBrush(QColor(0, 120, 215)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 4, 4);
        
        painter.setPen(QPen(Qt::white, 2));
        painter.drawText(pixmap.rect(), Qt::AlignCenter, "e");
    }
    else if (appName == "brave") {
        // Brave-like icon
        painter.setBrush(QBrush(QColor(251, 84, 43)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawEllipse(2, 2, ICON_SIZE-4, ICON_SIZE-4);
        
        // Draw triangle
        QPolygon triangle;
        triangle << QPoint(ICON_SIZE/2, 6) << QPoint(6, ICON_SIZE-6) << QPoint(ICON_SIZE-6, ICON_SIZE-6);
        painter.setBrush(QBrush(Qt::white));
        painter.drawPolygon(triangle);
    }
    else if (appName == "powershell") {
        // PowerShell-like icon
        painter.setBrush(QBrush(QColor(1, 36, 86)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 3, 3);
        
        painter.setPen(QPen(Qt::white, 2));
        painter.drawText(6, ICON_SIZE/2 + 2, "PS");
    }
    else if (appName == "explorer") {
        // File Explorer-like icon
        painter.setBrush(QBrush(QColor(255, 193, 7)));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 2, 2);
        
        painter.setBrush(QBrush(Qt::white));
        painter.drawRect(4, 6, ICON_SIZE-8, 2);
        painter.drawRect(4, 10, ICON_SIZE-8, 2);
        painter.drawRect(4, 14, ICON_SIZE-8, 2);
    }
    else {
        // Default icon
        painter.setBrush(QBrush(color));
        painter.setPen(QPen(Qt::white, 1));
        painter.drawRoundedRect(2, 2, ICON_SIZE-4, ICON_SIZE-4, 4, 4);
        
        QFont font = painter.font();
        font.setBold(true);
        font.setPointSize(8);
        painter.setFont(font);
        painter.setPen(Qt::white);
        painter.drawText(pixmap.rect(), Qt::AlignCenter, appName.left(1).toUpper());
    }
    
    return QIcon(pixmap);
}

void ApplicationLauncher::launchChrome()
{
    // TODO: Integrate with existing HiddenDesktop.cpp Chrome launch function
    emit applicationLaunched("Google Chrome", 1234); // Mock PID for now
    QMessageBox::information(this, "Application Launched", "Google Chrome launched successfully!");
}

void ApplicationLauncher::launchFirefox()
{
    // TODO: Integrate with existing HiddenDesktop.cpp Firefox launch function
    emit applicationLaunched("Mozilla Firefox", 1235);
    QMessageBox::information(this, "Application Launched", "Mozilla Firefox launched successfully!");
}

void ApplicationLauncher::launchEdge()
{
    // TODO: Integrate with existing HiddenDesktop.cpp Edge launch function
    emit applicationLaunched("Microsoft Edge", 1236);
    QMessageBox::information(this, "Application Launched", "Microsoft Edge launched successfully!");
}

void ApplicationLauncher::launchBrave()
{
    // TODO: Integrate with existing HiddenDesktop.cpp Brave launch function
    emit applicationLaunched("Brave Browser", 1237);
    QMessageBox::information(this, "Application Launched", "Brave Browser launched successfully!");
}

void ApplicationLauncher::launchPowerShell()
{
    // TODO: Integrate with existing HiddenDesktop.cpp PowerShell launch function
    emit applicationLaunched("Windows PowerShell", 1238);
    QMessageBox::information(this, "Application Launched", "Windows PowerShell launched successfully!");
}

void ApplicationLauncher::launchFileExplorer()
{
    // TODO: Integrate with existing HiddenDesktop.cpp File Explorer launch function
    emit applicationLaunched("File Explorer", 1239);
    QMessageBox::information(this, "Application Launched", "File Explorer launched successfully!");
}
