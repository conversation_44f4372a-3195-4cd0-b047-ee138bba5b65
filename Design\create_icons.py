#!/usr/bin/env python3
"""
Simple script to create placeholder icon files for the HVNC Controller Qt6 application.
Creates 24x24 PNG icons with colored backgrounds and text labels.
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

import os

def create_simple_icon(filename, color, text, size=24):
    """Create a simple colored icon with text"""
    if PIL_AVAILABLE:
        # Create with PIL if available
        img = Image.new('RGBA', (size, size), color + (255,))
        draw = ImageDraw.Draw(img)
        
        # Try to use a font, fall back to default if not available
        try:
            font = ImageFont.truetype("arial.ttf", size//3)
        except:
            font = ImageFont.load_default()
        
        # Get text size and center it
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2
        
        draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
        img.save(filename, 'PNG')
    else:
        # Create a minimal PNG file manually
        create_minimal_png(filename, size, color)

def create_minimal_png(filename, size, color):
    """Create a minimal PNG file without PIL"""
    import struct
    import zlib
    
    # Create RGBA data
    rgba_data = bytearray()
    for y in range(size):
        for x in range(size):
            rgba_data.extend(color + (255,))  # RGBA
    
    # Compress the data
    compressor = zlib.compressobj()
    png_data = compressor.compress(rgba_data)
    png_data += compressor.flush()
    
    # Create PNG file structure
    def write_chunk(f, chunk_type, data):
        f.write(struct.pack('>I', len(data)))
        f.write(chunk_type)
        f.write(data)
        crc = zlib.crc32(chunk_type + data) & 0xffffffff
        f.write(struct.pack('>I', crc))
    
    with open(filename, 'wb') as f:
        # PNG signature
        f.write(b'\x89PNG\r\n\x1a\n')
        
        # IHDR chunk
        ihdr = struct.pack('>IIBBBBB', size, size, 8, 6, 0, 0, 0)
        write_chunk(f, b'IHDR', ihdr)
        
        # IDAT chunk
        write_chunk(f, b'IDAT', png_data)
        
        # IEND chunk
        write_chunk(f, b'IEND', b'')

def main():
    icons_dir = "resources/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    # Define icons with colors and labels
    icons = [
        ("hvnc_icon.png", (42, 130, 218), "H"),
        ("chrome.png", (66, 133, 244), "C"),
        ("firefox.png", (255, 149, 0), "F"),
        ("edge.png", (0, 120, 215), "E"),
        ("brave.png", (251, 84, 43), "B"),
        ("powershell.png", (1, 36, 86), "PS"),
        ("explorer.png", (255, 193, 7), "Ex"),
        ("settings.png", (128, 128, 128), "⚙"),
        ("connect.png", (76, 175, 80), "→"),
        ("disconnect.png", (244, 67, 54), "×"),
    ]
    
    print("Creating placeholder icons...")
    for filename, color, text in icons:
        filepath = os.path.join(icons_dir, filename)
        create_simple_icon(filepath, color, text)
        print(f"Created: {filepath}")
    
    print(f"\nCreated {len(icons)} placeholder icons in {icons_dir}/")
    print("Icons are 24x24 PNG files with colored backgrounds and text labels.")
    
    if not PIL_AVAILABLE:
        print("\nNote: PIL/Pillow not available, created minimal PNG files.")
        print("For better quality icons, install Pillow: pip install Pillow")

if __name__ == "__main__":
    main()
