cmake_minimum_required(VERSION 3.16)

project(HVNCController VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt6 components
find_package(Qt6 REQUIRED COMPONENTS Core Widgets Network)

# Enable Qt MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../common)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/../Client)

# Source files
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/SplashScreen.cpp
    src/SettingsDialog.cpp
    src/RemoteDesktopWidget.cpp
    src/ApplicationLauncher.cpp
    src/HVNCClient.cpp
    src/PerformanceMonitor.cpp
    src/ConnectionManager.cpp
)

# Header files
set(HEADERS
    src/MainWindow.h
    src/SplashScreen.h
    src/SettingsDialog.h
    src/RemoteDesktopWidget.h
    src/ApplicationLauncher.h
    src/HVNCClient.h
    src/PerformanceMonitor.h
    src/ConnectionManager.h
)

# UI files
set(UI_FILES
    ui/MainWindow.ui
    ui/SettingsDialog.ui
)

# Resource files
set(RESOURCES
    resources/resources.qrc
)

# Add executable
add_executable(HVNCController
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCES}
)

# Link Qt6 libraries
target_link_libraries(HVNCController
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
)

# Windows-specific libraries
if(WIN32)
    target_link_libraries(HVNCController
        kernel32
        user32
        gdi32
        advapi32
        shell32
        ole32
        oleaut32
        uuid
        Gdiplus
    )
endif()

# Set application properties
set_target_properties(HVNCController PROPERTIES
    WIN32_EXECUTABLE TRUE
    OUTPUT_NAME "HVNC Controller"
)

# Copy resources to build directory
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/resources DESTINATION ${CMAKE_CURRENT_BINARY_DIR})
