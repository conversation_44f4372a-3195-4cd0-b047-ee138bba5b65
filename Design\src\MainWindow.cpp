#include "MainWindow.h"
#include "ApplicationLauncher.h"
#include "RemoteDesktopWidget.h"
#include "SettingsDialog.h"
#include "HVNCClient.h"
#include "PerformanceMonitor.h"
#include "ConnectionManager.h"

#include <QApplication>
#include <QMessageBox>
#include <QKeySequence>
#include <QSplitter>
#include <QHBoxLayout>
#include <QVBoxLayout>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_splitter(nullptr)
    , m_sidebarFrame(nullptr)
    , m_sidebarLayout(nullptr)
    , m_sidebarScrollArea(nullptr)
    , m_sidebarContent(nullptr)
    , m_connectionGroup(nullptr)
    , m_imageQualityGroup(nullptr)
    , m_performanceGroup(nullptr)
    , m_applicationGroup(nullptr)
    , m_contentFrame(nullptr)
    , m_contentLayout(nullptr)
    , m_remoteDesktop(nullptr)
    , m_fileMenu(nullptr)
    , m_viewMenu(nullptr)
    , m_helpMenu(nullptr)
    , m_settingsAction(nullptr)
    , m_exitAction(nullptr)
    , m_alwaysOnTopAction(nullptr)
    , m_aboutAction(nullptr)
    , m_connectionStatusLabel(nullptr)
    , m_fpsLabel(nullptr)
    , m_latencyLabel(nullptr)
    , m_cpuLabel(nullptr)
    , m_memoryLabel(nullptr)
    , m_trayIcon(nullptr)
    , m_trayMenu(nullptr)
    , m_hvncClient(nullptr)
    , m_performanceMonitor(nullptr)
    , m_connectionManager(nullptr)
    , m_settingsDialog(nullptr)
    , m_statusUpdateTimer(new QTimer(this))
    , m_isConnected(false)
    , m_alwaysOnTop(false)
{
    setWindowTitle("HVNC Controller");
    setMinimumSize(1000, 700);
    resize(1400, 900);

    try {
        qDebug() << "MainWindow: Starting initialization...";

        // Initialize core components with error handling
        qDebug() << "MainWindow: Creating HVNC client...";
        m_hvncClient = new HVNCClient(this);

        qDebug() << "MainWindow: Creating performance monitor...";
        m_performanceMonitor = new PerformanceMonitor(this);

        qDebug() << "MainWindow: Creating connection manager...";
        m_connectionManager = new ConnectionManager(this);

        qDebug() << "MainWindow: Setting up UI...";
        setupUI();

        qDebug() << "MainWindow: Setting up menu bar...";
        setupMenuBar();

        qDebug() << "MainWindow: Setting up status bar...";
        setupStatusBar();

        qDebug() << "MainWindow: Setting up system tray...";
        setupSystemTray();

        qDebug() << "MainWindow: Connecting signals...";
        connectSignals();

        qDebug() << "MainWindow: Starting status update timer...";
        // Start status update timer
        m_statusUpdateTimer->start(STATUS_UPDATE_INTERVAL);

        qDebug() << "MainWindow: Initialization complete!";
        // Set initial status
        if (statusBar()) {
            statusBar()->showMessage("HVNC Controller initialized - Ready to connect", 5000);
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Error initializing MainWindow:" << e.what();
        if (statusBar()) {
            statusBar()->showMessage("Error during initialization", 5000);
        }
        throw; // Re-throw to be caught by main()
    }
}

MainWindow::~MainWindow() = default;

void MainWindow::setupUI()
{
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    m_mainLayout = new QHBoxLayout(m_centralWidget);
    m_mainLayout->setContentsMargins(0, 0, 0, 0);
    m_mainLayout->setSpacing(0);
    
    m_splitter = new QSplitter(Qt::Horizontal, this);
    m_mainLayout->addWidget(m_splitter);
    
    createLeftSidebar();
    createMainContent();
    
    // Set splitter proportions
    m_splitter->setSizes({SIDEBAR_WIDTH, width() - SIDEBAR_WIDTH});
    m_splitter->setCollapsible(0, false); // Don't allow sidebar to collapse
}

void MainWindow::createLeftSidebar()
{
    m_sidebarFrame = new QFrame(this);
    m_sidebarFrame->setFrameStyle(QFrame::NoFrame);
    m_sidebarFrame->setFixedWidth(SIDEBAR_WIDTH);
    m_sidebarFrame->setStyleSheet(R"(
        QFrame {
            background-color: #0a0a0a;
            border-right: 2px solid #00ff00;
        }
    )");

    m_sidebarLayout = new QVBoxLayout(m_sidebarFrame);
    m_sidebarLayout->setContentsMargins(0, 0, 0, 0);
    m_sidebarLayout->setSpacing(0);

    // Create scroll area for sidebar content
    m_sidebarScrollArea = new QScrollArea(this);
    m_sidebarScrollArea->setWidgetResizable(true);
    m_sidebarScrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_sidebarScrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    m_sidebarScrollArea->setFrameStyle(QFrame::NoFrame);
    m_sidebarScrollArea->setStyleSheet(R"(
        QScrollArea {
            background-color: #0a0a0a;
            border: none;
        }
        QScrollBar:vertical {
            background-color: #1a1a1a;
            width: 8px;
            border: none;
        }
        QScrollBar::handle:vertical {
            background-color: #00ff00;
            border-radius: 0px;
            min-height: 20px;
        }
        QScrollBar::handle:vertical:hover {
            background-color: #00cc00;
        }
    )");

    // Create content widget for scroll area
    m_sidebarContent = new QWidget();
    m_sidebarContent->setStyleSheet("background-color: #0a0a0a;");

    QVBoxLayout *contentLayout = new QVBoxLayout(m_sidebarContent);
    contentLayout->setContentsMargins(8, 8, 8, 8);
    contentLayout->setSpacing(8);

    // Create all settings sections
    createConnectionSettings();
    createImageQualitySettings();
    createPerformanceSettings();
    createApplicationLauncher();

    // Add sections to content layout
    contentLayout->addWidget(m_connectionGroup);
    contentLayout->addWidget(m_imageQualityGroup);
    contentLayout->addWidget(m_performanceGroup);
    contentLayout->addWidget(m_applicationGroup);
    contentLayout->addStretch();

    // Set content widget to scroll area
    m_sidebarScrollArea->setWidget(m_sidebarContent);
    m_sidebarLayout->addWidget(m_sidebarScrollArea);

    m_splitter->addWidget(m_sidebarFrame);
}

void MainWindow::createMainContent()
{
    m_contentFrame = new QFrame(this);
    m_contentFrame->setFrameStyle(QFrame::NoFrame);
    m_contentFrame->setStyleSheet("QFrame { background-color: #2b2b2b; }");
    
    m_contentLayout = new QVBoxLayout(m_contentFrame);
    m_contentLayout->setContentsMargins(0, 0, 0, 0);
    m_contentLayout->setSpacing(0);
    
    // Remote desktop widget
    m_remoteDesktop = new RemoteDesktopWidget(this);
    m_contentLayout->addWidget(m_remoteDesktop);
    
    m_splitter->addWidget(m_contentFrame);
}

void MainWindow::setupMenuBar()
{
    // File menu
    m_fileMenu = menuBar()->addMenu("&File");
    
    m_settingsAction = new QAction("&Settings...", this);
    m_settingsAction->setShortcut(QKeySequence::Preferences);
    m_settingsAction->setStatusTip("Open settings dialog");
    m_fileMenu->addAction(m_settingsAction);
    
    m_fileMenu->addSeparator();
    
    m_exitAction = new QAction("E&xit", this);
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_exitAction->setStatusTip("Exit the application");
    m_fileMenu->addAction(m_exitAction);
    
    // View menu
    m_viewMenu = menuBar()->addMenu("&View");
    
    m_alwaysOnTopAction = new QAction("Always on &Top", this);
    m_alwaysOnTopAction->setCheckable(true);
    m_alwaysOnTopAction->setStatusTip("Keep window always on top");
    m_viewMenu->addAction(m_alwaysOnTopAction);
    
    // Help menu
    m_helpMenu = menuBar()->addMenu("&Help");
    
    m_aboutAction = new QAction("&About", this);
    m_aboutAction->setStatusTip("Show application information");
    m_helpMenu->addAction(m_aboutAction);
}

void MainWindow::setupStatusBar()
{
    // Connection status
    m_connectionStatusLabel = new QLabel("Disconnected");
    m_connectionStatusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold;");
    statusBar()->addWidget(m_connectionStatusLabel);
    
    statusBar()->addPermanentWidget(new QLabel(" | "));
    
    // FPS counter
    m_fpsLabel = new QLabel("FPS: 0");
    m_fpsLabel->setMinimumWidth(60);
    statusBar()->addPermanentWidget(m_fpsLabel);
    
    statusBar()->addPermanentWidget(new QLabel(" | "));
    
    // Latency
    m_latencyLabel = new QLabel("Latency: 0ms");
    m_latencyLabel->setMinimumWidth(80);
    statusBar()->addPermanentWidget(m_latencyLabel);
    
    statusBar()->addPermanentWidget(new QLabel(" | "));
    
    // CPU usage
    m_cpuLabel = new QLabel("CPU: 0%");
    m_cpuLabel->setMinimumWidth(60);
    statusBar()->addPermanentWidget(m_cpuLabel);
    
    statusBar()->addPermanentWidget(new QLabel(" | "));
    
    // Memory usage
    m_memoryLabel = new QLabel("Memory: 0%");
    m_memoryLabel->setMinimumWidth(80);
    statusBar()->addPermanentWidget(m_memoryLabel);
}

void MainWindow::setupSystemTray()
{
    try {
        if (!QSystemTrayIcon::isSystemTrayAvailable()) {
            qDebug() << "System tray not available";
            return;
        }

        m_trayIcon = new QSystemTrayIcon(this);

        // Try to load icon, use default if not found
        QIcon trayIcon(":/icons/hvnc_icon.png");
        if (trayIcon.isNull()) {
            // Create a simple default icon if resource not found
            QPixmap pixmap(16, 16);
            pixmap.fill(QColor(0, 255, 0));
            trayIcon = QIcon(pixmap);
        }

        m_trayIcon->setIcon(trayIcon);
        m_trayIcon->setToolTip("HVNC Controller");

        m_trayMenu = new QMenu(this);
        m_trayMenu->addAction("Show", this, &MainWindow::show);
        m_trayMenu->addAction("Hide", this, &MainWindow::hide);
        m_trayMenu->addSeparator();
        m_trayMenu->addAction("Exit", this, &MainWindow::close);

        m_trayIcon->setContextMenu(m_trayMenu);
        m_trayIcon->show();
    }
    catch (const std::exception& e) {
        qDebug() << "Error setting up system tray:" << e.what();
        // Continue without system tray if there's an error
    }
}

void MainWindow::connectSignals()
{
    // Menu actions
    connect(m_settingsAction, &QAction::triggered, this, &MainWindow::showSettings);
    connect(m_exitAction, &QAction::triggered, this, &MainWindow::close);
    connect(m_alwaysOnTopAction, &QAction::triggered, this, &MainWindow::toggleAlwaysOnTop);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::showAbout);
    
    // Application launcher connections are now handled in createApplicationLauncher()

    // HVNC Client connections
    connect(m_hvncClient, &HVNCClient::connected, this, [this]() {
        statusBar()->showMessage("Connected to HVNC server", 3000);
    });

    connect(m_hvncClient, &HVNCClient::disconnected, this, [this]() {
        statusBar()->showMessage("Disconnected from HVNC server", 3000);
    });

    connect(m_hvncClient, &HVNCClient::serverStarted, this, [this](const QString &host, int port) {
        statusBar()->showMessage(QString("HVNC server started on %1:%2").arg(host).arg(port), 5000);
    });

    connect(m_hvncClient, &HVNCClient::serverStopped, this, [this]() {
        statusBar()->showMessage("HVNC server stopped", 3000);
    });

    connect(m_hvncClient, &HVNCClient::serverError, this, [this](const QString &error) {
        statusBar()->showMessage(QString("Server error: %1").arg(error), 5000);
    });

    connect(m_hvncClient, &HVNCClient::errorOccurred, this, [this](const QString &error) {
        statusBar()->showMessage(QString("Connection error: %1").arg(error), 5000);
    });

    connect(m_hvncClient, &HVNCClient::desktopImageReceived, m_remoteDesktopWidget, &RemoteDesktopWidget::updateDesktopImage);

    // Connection manager
    connect(m_connectionManager, &ConnectionManager::connectionStateChanged,
            this, &MainWindow::onConnectionStateChanged);

    // Performance monitor
    connect(m_performanceMonitor, &PerformanceMonitor::performanceUpdate,
            this, &MainWindow::onPerformanceUpdate);
    
    // System tray
    if (m_trayIcon) {
        connect(m_trayIcon, &QSystemTrayIcon::activated,
                this, &MainWindow::onTrayIconActivated);
    }
    
    // Status update timer
    connect(m_statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateStatusBar);
}

void MainWindow::createConnectionSettings()
{
    m_connectionGroup = new QGroupBox("CONNECTION", this);
    m_connectionGroup->setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #00ff00;
            margin-top: 8px;
            padding-top: 12px;
            background-color: #050505;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
    )");

    QVBoxLayout *layout = new QVBoxLayout(m_connectionGroup);
    layout->setContentsMargins(8, 12, 8, 8);
    layout->setSpacing(6);

    // Server IP
    QLabel *ipLabel = new QLabel("SERVER IP:", this);
    ipLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-weight: bold;");
    layout->addWidget(ipLabel);

    QLineEdit *ipEdit = new QLineEdit("127.0.0.1", this);
    ipEdit->setStyleSheet(R"(
        QLineEdit {
            background-color: #000000;
            border: 1px solid #00ff00;
            padding: 4px;
            font-size: 9px;
            font-family: 'Consolas', monospace;
        }
    )");
    layout->addWidget(ipEdit);

    // Port
    QLabel *portLabel = new QLabel("PORT:", this);
    portLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-weight: bold;");
    layout->addWidget(portLabel);

    QLineEdit *portEdit = new QLineEdit("4444", this);
    portEdit->setStyleSheet(R"(
        QLineEdit {
            background-color: #000000;
            border: 1px solid #00ff00;
            padding: 4px;
            font-size: 9px;
            font-family: 'Consolas', monospace;
        }
    )");
    layout->addWidget(portEdit);

    // Connect button
    QPushButton *connectBtn = new QPushButton("CONNECT", this);
    connectBtn->setStyleSheet(R"(
        QPushButton {
            background-color: #001100;
            border: 1px solid #00ff00;
            padding: 6px;
            font-size: 9px;
            font-weight: bold;
            color: #00ff00;
        }
        QPushButton:hover {
            background-color: #003300;
            box-shadow: 0 0 8px #00ff00;
        }
    )");
    layout->addWidget(connectBtn);

    // Disconnect button
    QPushButton *disconnectBtn = new QPushButton("DISCONNECT", this);
    disconnectBtn->setStyleSheet(R"(
        QPushButton {
            background-color: #110000;
            border: 1px solid #ff0000;
            padding: 6px;
            font-size: 9px;
            font-weight: bold;
            color: #ff0000;
        }
        QPushButton:hover {
            background-color: #330000;
            box-shadow: 0 0 8px #ff0000;
        }
    )");
    layout->addWidget(disconnectBtn);

    // Server section separator
    QLabel *serverSeparator = new QLabel("SERVER CONTROL:", this);
    serverSeparator->setStyleSheet("font-size: 9px; color: #00ff00; font-weight: bold; margin-top: 8px;");
    layout->addWidget(serverSeparator);

    // Start Server button
    QPushButton *startServerBtn = new QPushButton("START SERVER", this);
    startServerBtn->setStyleSheet(R"(
        QPushButton {
            background-color: #001100;
            border: 1px solid #00ff00;
            padding: 6px;
            font-size: 9px;
            font-weight: bold;
            color: #00ff00;
        }
        QPushButton:hover {
            background-color: #003300;
            box-shadow: 0 0 8px #00ff00;
        }
    )");
    layout->addWidget(startServerBtn);

    // Stop Server button
    QPushButton *stopServerBtn = new QPushButton("STOP SERVER", this);
    stopServerBtn->setStyleSheet(R"(
        QPushButton {
            background-color: #110000;
            border: 1px solid #ff0000;
            padding: 6px;
            font-size: 9px;
            font-weight: bold;
            color: #ff0000;
        }
        QPushButton:hover {
            background-color: #330000;
            box-shadow: 0 0 8px #ff0000;
        }
    )");
    layout->addWidget(stopServerBtn);

    // Connect button signals
    connect(connectBtn, &QPushButton::clicked, [this, ipEdit, portEdit]() {
        QString ip = ipEdit->text();
        int port = portEdit->text().toInt();
        statusBar()->showMessage(QString("Connecting to %1:%2...").arg(ip).arg(port), 3000);

        if (m_hvncClient->connectToServer(ip, port)) {
            statusBar()->showMessage(QString("Connected to HVNC server %1:%2").arg(ip).arg(port), 5000);
        } else {
            statusBar()->showMessage("Failed to connect to HVNC server", 5000);
        }
    });

    connect(disconnectBtn, &QPushButton::clicked, [this]() {
        statusBar()->showMessage("Disconnecting from server...", 2000);
        m_hvncClient->disconnectFromServer();
        statusBar()->showMessage("Disconnected from server", 3000);
    });

    connect(startServerBtn, &QPushButton::clicked, [this, ipEdit, portEdit]() {
        QString ip = ipEdit->text();
        int port = portEdit->text().toInt();
        statusBar()->showMessage(QString("Starting HVNC server on %1:%2...").arg(ip).arg(port), 3000);

        if (m_hvncClient->startHVNCServer(ip, port)) {
            statusBar()->showMessage(QString("HVNC server started on %1:%2").arg(ip).arg(port), 5000);
        } else {
            statusBar()->showMessage("Failed to start HVNC server", 5000);
        }
    });

    connect(stopServerBtn, &QPushButton::clicked, [this]() {
        statusBar()->showMessage("Stopping HVNC server...", 2000);
        m_hvncClient->stopHVNCServer();
        statusBar()->showMessage("HVNC server stopped", 3000);
    });
}

void MainWindow::createImageQualitySettings()
{
    m_imageQualityGroup = new QGroupBox("IMAGE QUALITY", this);
    m_imageQualityGroup->setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #00ff00;
            margin-top: 8px;
            padding-top: 12px;
            background-color: #050505;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
    )");

    QVBoxLayout *layout = new QVBoxLayout(m_imageQualityGroup);
    layout->setContentsMargins(8, 12, 8, 8);
    layout->setSpacing(6);

    // Compression
    QLabel *compLabel = new QLabel("COMPRESSION:", this);
    compLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-weight: bold;");
    layout->addWidget(compLabel);

    QSlider *compSlider = new QSlider(Qt::Horizontal, this);
    compSlider->setRange(1, 100);
    compSlider->setValue(85);
    compSlider->setStyleSheet(R"(
        QSlider::groove:horizontal {
            border: 1px solid #00ff00;
            height: 4px;
            background: #000000;
        }
        QSlider::handle:horizontal {
            background: #00ff00;
            border: 1px solid #00ff00;
            width: 12px;
            margin: -4px 0;
        }
    )");
    layout->addWidget(compSlider);

    // Resolution scale
    QLabel *scaleLabel = new QLabel("SCALE:", this);
    scaleLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-weight: bold;");
    layout->addWidget(scaleLabel);

    QComboBox *scaleCombo = new QComboBox(this);
    scaleCombo->addItems({"25%", "50%", "75%", "100%"});
    scaleCombo->setCurrentText("100%");
    scaleCombo->setStyleSheet(R"(
        QComboBox {
            background-color: #000000;
            border: 1px solid #00ff00;
            padding: 4px;
            font-size: 9px;
            font-family: 'Consolas', monospace;
        }
    )");
    layout->addWidget(scaleCombo);
}

void MainWindow::createPerformanceSettings()
{
    m_performanceGroup = new QGroupBox("PERFORMANCE", this);
    m_performanceGroup->setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #00ff00;
            margin-top: 8px;
            padding-top: 12px;
            background-color: #050505;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
    )");

    QVBoxLayout *layout = new QVBoxLayout(m_performanceGroup);
    layout->setContentsMargins(8, 12, 8, 8);
    layout->setSpacing(4);

    // FPS display
    QLabel *fpsLabel = new QLabel("FPS: 0", this);
    fpsLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-family: 'Consolas', monospace;");
    layout->addWidget(fpsLabel);

    // Latency display
    QLabel *latencyLabel = new QLabel("LATENCY: 0ms", this);
    latencyLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-family: 'Consolas', monospace;");
    layout->addWidget(latencyLabel);

    // CPU display
    QLabel *cpuLabel = new QLabel("CPU: 0%", this);
    cpuLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-family: 'Consolas', monospace;");
    layout->addWidget(cpuLabel);

    // Memory display
    QLabel *memLabel = new QLabel("MEM: 0%", this);
    memLabel->setStyleSheet("font-size: 9px; color: #00ff00; font-family: 'Consolas', monospace;");
    layout->addWidget(memLabel);
}

void MainWindow::createApplicationLauncher()
{
    m_applicationGroup = new QGroupBox("APPLICATIONS", this);
    m_applicationGroup->setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #00ff00;
            margin-top: 8px;
            padding-top: 12px;
            background-color: #050505;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
    )");

    QVBoxLayout *layout = new QVBoxLayout(m_applicationGroup);
    layout->setContentsMargins(8, 12, 8, 8);
    layout->setSpacing(4);

    // Application buttons
    QStringList apps = {"CHROME", "FIREFOX", "EDGE", "BRAVE", "POWERSHELL", "EXPLORER"};

    for (const QString &app : apps) {
        QPushButton *btn = new QPushButton(app, this);
        btn->setStyleSheet(R"(
            QPushButton {
                background-color: #001100;
                border: 1px solid #00ff00;
                padding: 4px;
                font-size: 8px;
                font-weight: bold;
                color: #00ff00;
                text-align: left;
                font-family: 'Consolas', monospace;
            }
            QPushButton:hover {
                background-color: #003300;
                box-shadow: 0 0 5px #00ff00;
            }
            QPushButton:pressed {
                background-color: #000800;
            }
        )");
        layout->addWidget(btn);

        // Connect button to launch function
        connect(btn, &QPushButton::clicked, [this, app]() {
            statusBar()->showMessage(QString("Launching %1...").arg(app), 2000);
        });
    }
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    try {
        if (m_trayIcon && m_trayIcon->isVisible()) {
            hide();
            event->ignore();
            m_trayIcon->showMessage("HVNC Controller", "Application minimized to tray", QSystemTrayIcon::Information, 2000);
        } else {
            // Clean shutdown
            if (m_connectionManager) {
                m_connectionManager->disconnectFromServer();
            }
            event->accept();
        }
    }
    catch (const std::exception& e) {
        qDebug() << "Error during close:" << e.what();
        event->accept(); // Force close if there's an error
    }
}

void MainWindow::onApplicationLaunched(const QString &appName, int pid)
{
    statusBar()->showMessage(QString("Launched %1 (PID: %2)").arg(appName).arg(pid), 3000);
}

void MainWindow::onConnectionStateChanged(bool connected)
{
    m_isConnected = connected;

    if (connected) {
        m_connectionStatusLabel->setText("Connected");
        m_connectionStatusLabel->setStyleSheet("color: #51cf66; font-weight: bold;");
    } else {
        m_connectionStatusLabel->setText("Disconnected");
        m_connectionStatusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold;");
    }
}

void MainWindow::onPerformanceUpdate(int fps, int latency, float cpuUsage, float memoryUsage)
{
    m_fpsLabel->setText(QString("FPS: %1").arg(fps));
    m_latencyLabel->setText(QString("Latency: %1ms").arg(latency));
    m_cpuLabel->setText(QString("CPU: %1%").arg(cpuUsage, 0, 'f', 1));
    m_memoryLabel->setText(QString("Memory: %1%").arg(memoryUsage, 0, 'f', 1));
}

void MainWindow::showSettings()
{
    if (!m_settingsDialog) {
        m_settingsDialog = new SettingsDialog(this);
    }
    m_settingsDialog->show();
    m_settingsDialog->raise();
    m_settingsDialog->activateWindow();
}

void MainWindow::toggleAlwaysOnTop()
{
    m_alwaysOnTop = !m_alwaysOnTop;

    Qt::WindowFlags flags = windowFlags();
    if (m_alwaysOnTop) {
        flags |= Qt::WindowStaysOnTopHint;
    } else {
        flags &= ~Qt::WindowStaysOnTopHint;
    }

    setWindowFlags(flags);
    show();
}

void MainWindow::showAbout()
{
    QMessageBox::about(this, "About HVNC Controller",
        "<h3>HVNC Controller v1.0.0</h3>"
        "<p>Professional remote desktop controller for HVNC systems.</p>"
        "<p>Built with Qt6 and modern C++20.</p>"
        "<p>© 2025 HVNC Systems</p>");
}

void MainWindow::onTrayIconActivated(QSystemTrayIcon::ActivationReason reason)
{
    if (reason == QSystemTrayIcon::DoubleClick) {
        if (isVisible()) {
            hide();
        } else {
            show();
            raise();
            activateWindow();
        }
    }
}

void MainWindow::updateStatusBar()
{
    // This will be called periodically to update status information
    // Implementation depends on the actual HVNC client integration
}
