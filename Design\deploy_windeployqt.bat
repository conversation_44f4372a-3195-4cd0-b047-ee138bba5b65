@echo off
echo Deploying HVNC Controller using Qt6 windeployqt tool...

REM Check if build exists
if not exist "build\Release\HVNC Controller.exe" (
    echo Error: Application not built yet!
    echo Please run build.bat first.
    pause
    exit /b 1
)

REM Determine Qt6 path
if "%CMAKE_PREFIX_PATH%"=="" (
    echo Detecting Qt6 installation...
    
    REM Try common Qt6 installation paths
    if exist "C:\Qt\6.5.3\msvc2019_64" set QT6_PATH=C:\Qt\6.5.3\msvc2019_64
    if exist "C:\Qt\6.5.0\msvc2022_64" set QT6_PATH=C:\Qt\6.5.0\msvc2022_64
    if exist "C:\Qt\6.4.0\msvc2022_64" set QT6_PATH=C:\Qt\6.4.0\msvc2022_64
    if exist "C:\Qt\6.6.0\msvc2022_64" set QT6_PATH=C:\Qt\6.6.0\msvc2022_64
) else (
    set QT6_PATH=%CMAKE_PREFIX_PATH%
)

if "%QT6_PATH%"=="" (
    echo Error: Qt6 installation not found!
    echo Please set QT6_PATH manually, for example:
    echo set QT6_PATH=C:\Qt\6.5.3\msvc2019_64
    pause
    exit /b 1
)

echo Using Qt6 path: %QT6_PATH%

REM Check if windeployqt exists
if not exist "%QT6_PATH%\bin\windeployqt.exe" (
    echo Error: windeployqt.exe not found at %QT6_PATH%\bin\windeployqt.exe
    echo Falling back to manual deployment...
    call deploy.bat
    exit /b %ERRORLEVEL%
)

echo Using Qt6 windeployqt tool for automatic deployment...

REM Add Qt6 bin to PATH temporarily
set PATH=%QT6_PATH%\bin;%PATH%

REM Change to build directory
cd build\Release

REM Run windeployqt with comprehensive options
echo Running windeployqt...
"%QT6_PATH%\bin\windeployqt.exe" ^
    --debug-and-release ^
    --compiler-runtime ^
    --qtlibs ^
    --qtpaths ^
    --verbose 2 ^
    "HVNC Controller.exe"

if %ERRORLEVEL% neq 0 (
    echo windeployqt failed! Trying manual deployment...
    cd ..\..
    call deploy.bat
    exit /b %ERRORLEVEL%
)

cd ..\..

echo.
echo Deployment completed successfully using windeployqt!
echo.
echo All Qt6 dependencies have been automatically resolved and copied.
echo You can now run: build\Release\HVNC Controller.exe
echo.

REM Test the application
echo Testing application launch...
cd build\Release
start "" "HVNC Controller.exe"
cd ..\..

echo Application launched! Check if it starts correctly.
echo.
pause
