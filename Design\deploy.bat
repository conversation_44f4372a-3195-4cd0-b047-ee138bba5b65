@echo off
echo Deploying HVNC Controller Qt6 Application...

REM Check if build exists
if not exist "build\Release\HVNC Controller.exe" (
    echo Error: Application not built yet!
    echo Please run build.bat first.
    pause
    exit /b 1
)

REM Determine Qt6 path
if "%CMAKE_PREFIX_PATH%"=="" (
    echo Detecting Qt6 installation...
    
    REM Try common Qt6 installation paths
    if exist "C:\Qt\6.5.3\msvc2019_64" set QT6_PATH=C:\Qt\6.5.3\msvc2019_64
    if exist "C:\Qt\6.5.0\msvc2022_64" set QT6_PATH=C:\Qt\6.5.0\msvc2022_64
    if exist "C:\Qt\6.4.0\msvc2022_64" set QT6_PATH=C:\Qt\6.4.0\msvc2022_64
    if exist "C:\Qt\6.6.0\msvc2022_64" set QT6_PATH=C:\Qt\6.6.0\msvc2022_64
) else (
    set QT6_PATH=%CMAKE_PREFIX_PATH%
)

if "%QT6_PATH%"=="" (
    echo Error: Qt6 installation not found!
    echo Please set QT6_PATH manually, for example:
    echo set QT6_PATH=C:\Qt\6.5.3\msvc2019_64
    pause
    exit /b 1
)

echo Using Qt6 path: %QT6_PATH%

REM Check if Qt6 bin directory exists
if not exist "%QT6_PATH%\bin" (
    echo Error: Qt6 bin directory not found at %QT6_PATH%\bin
    pause
    exit /b 1
)

echo Copying Qt6 DLLs...

REM Core Qt6 DLLs
copy "%QT6_PATH%\bin\Qt6Core.dll" "build\Release\" >nul 2>&1
copy "%QT6_PATH%\bin\Qt6Gui.dll" "build\Release\" >nul 2>&1
copy "%QT6_PATH%\bin\Qt6Widgets.dll" "build\Release\" >nul 2>&1
copy "%QT6_PATH%\bin\Qt6Network.dll" "build\Release\" >nul 2>&1

REM Additional Qt6 DLLs that might be needed
copy "%QT6_PATH%\bin\Qt6Core5Compat.dll" "build\Release\" >nul 2>&1

REM Platform plugins (required for Qt6 applications)
if not exist "build\Release\platforms" mkdir "build\Release\platforms"
copy "%QT6_PATH%\plugins\platforms\qwindows.dll" "build\Release\platforms\" >nul 2>&1
copy "%QT6_PATH%\plugins\platforms\qminimal.dll" "build\Release\platforms\" >nul 2>&1

REM Image format plugins (for icon support)
if not exist "build\Release\imageformats" mkdir "build\Release\imageformats"
copy "%QT6_PATH%\plugins\imageformats\qico.dll" "build\Release\imageformats\" >nul 2>&1
copy "%QT6_PATH%\plugins\imageformats\qjpeg.dll" "build\Release\imageformats\" >nul 2>&1
copy "%QT6_PATH%\plugins\imageformats\qpng.dll" "build\Release\imageformats\" >nul 2>&1

REM Style plugins (for better Windows integration)
if not exist "build\Release\styles" mkdir "build\Release\styles"
copy "%QT6_PATH%\plugins\styles\qwindowsvistastyle.dll" "build\Release\styles\" >nul 2>&1

REM Network plugins
if not exist "build\Release\networkinformation" mkdir "build\Release\networkinformation"
copy "%QT6_PATH%\plugins\networkinformation\qnetworklistmanager.dll" "build\Release\networkinformation\" >nul 2>&1

REM TLS plugins (for secure network connections)
if not exist "build\Release\tls" mkdir "build\Release\tls"
copy "%QT6_PATH%\plugins\tls\qschannelbackend.dll" "build\Release\tls\" >nul 2>&1
copy "%QT6_PATH%\plugins\tls\qopensslbackend.dll" "build\Release\tls\" >nul 2>&1

REM Generic plugins
if not exist "build\Release\generic" mkdir "build\Release\generic"
copy "%QT6_PATH%\plugins\generic\qtuiotouchplugin.dll" "build\Release\generic\" >nul 2>&1

REM Check which DLLs were successfully copied
echo.
echo Checking copied files...
echo Core DLLs:
if exist "build\Release\Qt6Core.dll" (echo   ✓ Qt6Core.dll) else (echo   ✗ Qt6Core.dll - MISSING!)
if exist "build\Release\Qt6Gui.dll" (echo   ✓ Qt6Gui.dll) else (echo   ✗ Qt6Gui.dll - MISSING!)
if exist "build\Release\Qt6Widgets.dll" (echo   ✓ Qt6Widgets.dll) else (echo   ✗ Qt6Widgets.dll - MISSING!)
if exist "build\Release\Qt6Network.dll" (echo   ✓ Qt6Network.dll) else (echo   ✗ Qt6Network.dll - MISSING!)

echo Platform plugins:
if exist "build\Release\platforms\qwindows.dll" (echo   ✓ platforms\qwindows.dll) else (echo   ✗ platforms\qwindows.dll - MISSING!)

echo Image format plugins:
if exist "build\Release\imageformats\qpng.dll" (echo   ✓ imageformats\qpng.dll) else (echo   ✗ imageformats\qpng.dll - MISSING!)

REM Check for Visual C++ Redistributable dependencies
echo.
echo Checking Visual C++ Redistributable dependencies...
where vcruntime140.dll >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo   ✓ Visual C++ Redistributable found in system PATH
) else (
    echo   ⚠ Visual C++ Redistributable may be needed
    echo     Download from: https://aka.ms/vs/17/release/vc_redist.x64.exe
)

echo.
echo Deployment completed!
echo.
echo You can now run the application from: build\Release\HVNC Controller.exe
echo All necessary Qt6 DLLs and plugins have been copied.
echo.

REM Try to run the application
echo Testing application launch...
cd build\Release
start "" "HVNC Controller.exe"
cd ..\..

echo Application launched! Check if it starts correctly.
echo.
pause
