@echo off
echo HVNC Controller - Qt6 GUI Application
echo =====================================

REM Check if application exists
if not exist "build\Release\HVNC Controller.exe" (
    echo Error: Application not found!
    echo Please run build.bat and deploy.bat first.
    pause
    exit /b 1
)

REM Check for required Qt6 DLLs
echo Checking Qt6 dependencies...
set MISSING_DLLS=0

if not exist "build\Release\Qt6Core.dll" (
    echo   ✗ Qt6Core.dll - MISSING!
    set MISSING_DLLS=1
) else (
    echo   ✓ Qt6Core.dll
)

if not exist "build\Release\Qt6Gui.dll" (
    echo   ✗ Qt6Gui.dll - MISSING!
    set MISSING_DLLS=1
) else (
    echo   ✓ Qt6Gui.dll
)

if not exist "build\Release\Qt6Widgets.dll" (
    echo   ✗ Qt6Widgets.dll - MISSING!
    set MISSING_DLLS=1
) else (
    echo   ✓ Qt6Widgets.dll
)

if not exist "build\Release\Qt6Network.dll" (
    echo   ✗ Qt6Network.dll - MISSING!
    set MISSING_DLLS=1
) else (
    echo   ✓ Qt6Network.dll
)

if not exist "build\Release\platforms\qwindows.dll" (
    echo   ✗ platforms\qwindows.dll - MISSING!
    set MISSING_DLLS=1
) else (
    echo   ✓ platforms\qwindows.dll
)

if %MISSING_DLLS% equ 1 (
    echo.
    echo Error: Missing required Qt6 DLLs!
    echo Please run deploy.bat to copy all dependencies.
    pause
    exit /b 1
)

echo.
echo All Qt6 dependencies found!
echo Starting HVNC Controller...
echo.

REM Change to the Release directory and run the application
cd build\Release
start "" "HVNC Controller.exe"

echo Application launched!
echo.
echo Features available:
echo - Professional splash screen with loading animation
echo - Dark theme interface with modern styling
echo - Application launcher sidebar (Chrome, Firefox, Edge, Brave, PowerShell, Explorer)
echo - Remote desktop display area with mouse/keyboard input
echo - Comprehensive settings panel with 3 tabs
echo - Real-time performance monitoring
echo - Connection management with auto-reconnect
echo - System tray support
echo.
echo To integrate with existing HVNC system:
echo 1. Replace mock implementations in ApplicationLauncher class
echo 2. Connect HVNCClient to your existing server protocol
echo 3. Use SimpleLogger for consistent logging
echo.
pause
